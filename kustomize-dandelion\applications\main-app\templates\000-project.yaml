---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: dandelion-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.argoAppSuffix }}
  # Finalizer that ensures that project is not deleted until it is not referenced by any application
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: dandelion-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.argoAppSuffix }}
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  destinations:
  - namespace: '*'
    server: '*'
  sourceRepos:
  - '*'
---
