{{ if .Values.dandelion.deployIngress }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cardano-submit-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "submit-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: cardano-submit-api
                port:
                  number: 8091
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "submit-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: cardano-submit-api
                port:
                  number: 8091
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "submit-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "submit-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cardano-graphql-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "graphql-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: cardano-graphql
                port:
                  number: 3100
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "graphql-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: cardano-graphql
                port:
                  number: 3100
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "graphql-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "graphql-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chisel-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "chisel-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: chisel-server
                port:
                  number: 40000
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "chisel-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: chisel-server
                port:
                  number: 40000
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "chisel-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "chisel-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ogmios-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "ogmios-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: ogmios
                port:
                  number: 1337
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "ogmios-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: ogmios
                port:
                  number: 1337
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "ogmios-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "ogmios-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: postgrest-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "postgrest-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: postgrest
                port:
                  number: 3000
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "postgrest-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: postgrest
                port:
                  number: 3000
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "postgrest-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "postgrest-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: koios-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "koios-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: koios-api
                port:
                  number: 3000
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "koios-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: koios-api
                port:
                  number: 3000
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "koios-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "koios-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rosetta-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "rosetta-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: cardano-rosetta
                port:
                  number: 8080
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "rosetta-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: cardano-rosetta
                port:
                  number: 8080
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "rosetta-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "rosetta-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
      #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: postgresws-api
  namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
spec:
  rules:
    - host: "postgresws-api.{{ .Values.dandelion.ingressBaseDomain }}"
      http:
        paths:
          - backend:
              service:
                name: postgres-ws
                port:
                  number: 3000
            path: /
            pathType: Prefix
    {{- if .Values.dandelion.ingressAlternativeDomain }}
    - host: "postgresws-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
      http:
        paths:
          - backend:
              service:
                name: postgres-ws
                port:
                  number: 3000
            path: /
            pathType: Prefix
    {{- end }}
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
    - hosts:
        - "postgresws-api.{{ .Values.dandelion.ingressBaseDomain }}"
        {{- if .Values.dandelion.ingressAlternativeDomain }}
        - "postgresws-api.{{ .Values.dandelion.ingressAlternativeDomain }}"
        {{- end }}
{{ end }}
