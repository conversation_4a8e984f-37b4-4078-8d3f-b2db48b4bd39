# Cardano Deployment Comparison

This document compares the two Cardano deployment options to help you choose the right one.

## Overview

| Feature | cardano-simple | kustomize-carnado |
|---------|----------------|-------------------|
| **Target Audience** | Beginners, getting started | Advanced users, production |
| **Complexity** | Low - single files | High - modular structure |
| **Setup Time** | 5 minutes | 30+ minutes |
| **Blockchain Knowledge Required** | None | Moderate to high |
| **Kubernetes Knowledge Required** | Basic | Advanced |
| **Customization** | Limited | Extensive |

## Detailed Comparison

### cardano-simple/
**Best for: Learning, testing, quick demos**

✅ **Advantages:**
- **Beginner-friendly**: No blockchain knowledge needed
- **Quick setup**: Deploy in 5 minutes with one command
- **Simple structure**: All configs in 3 files
- **Clear documentation**: Step-by-step guides with explanations
- **Built-in troubleshooting**: Automated status checks
- **Mithril fast sync**: Still includes fast blockchain sync
- **Self-contained**: No external dependencies

❌ **Limitations:**
- **Basic configuration**: Limited customization options
- **Single network**: Only preprod testnet
- **No advanced features**: Missing monitoring, scaling, etc.
- **Fixed resources**: Hard-coded resource limits
- **Simple security**: Basic password management

**File Structure:**
```
cardano-simple/
├── README.md              # Beginner-friendly guide
├── TROUBLESHOOTING.md     # Common problems & solutions
├── kustomization.yaml     # Simple, single overlay
├── postgres.yaml          # Basic PostgreSQL
├── cardano-node.yaml      # Pre-configured node
├── cardano-db-sync.yaml   # Simple db-sync
├── deploy.sh              # One-click deployment
└── check-status.sh        # Status checker
```

### kustomize-carnado/
**Best for: Production, advanced users, customization**

✅ **Advantages:**
- **Production-ready**: Full enterprise features
- **Highly customizable**: Modular, configurable components
- **Multiple networks**: Support for mainnet, testnet, preprod
- **Advanced features**: Network patches, RBAC, monitoring
- **Scalable**: Resource optimization and scaling options
- **Professional structure**: Follows Kustomize best practices
- **Comprehensive**: Includes all Cardano ecosystem components

❌ **Limitations:**
- **Complex setup**: Requires understanding of Kubernetes and Cardano
- **Steep learning curve**: Need to understand network patches, components
- **More maintenance**: Multiple files to manage and update
- **Blockchain knowledge**: Need to understand network configurations
- **Time investment**: Takes longer to understand and deploy

**File Structure:**
```
kustomize-carnado/
├── base/
│   ├── common/            # Shared configurations
│   ├── cardano-node/      # Modular node setup
│   ├── cardano-db-sync/   # Advanced db-sync
│   └── network-specific-patches/
│       └── testnet-preprod/  # Network customizations
├── overlays/
│   └── testnet-preprod/   # Environment-specific config
├── README.md              # Technical documentation
└── validate.sh            # Configuration validator
```

## When to Use Which?

### Choose cardano-simple if you:
- 🎯 Want to **learn about Cardano** without complexity
- 🚀 Need a **quick demo** or proof of concept
- 📚 Are **new to blockchain** or Kubernetes
- 🔧 Want **minimal maintenance** and setup
- 🎮 Are **experimenting** or building prototypes
- ⏰ Have **limited time** to get started

### Choose kustomize-carnado if you:
- 🏢 Building **production systems**
- 🔧 Need **extensive customization**
- 🌐 Want **multiple network support**
- 📊 Require **monitoring and observability**
- 🔒 Need **enterprise security features**
- 👥 Have a **team with Kubernetes expertise**
- 🎯 Building **long-term infrastructure**

## Migration Path

You can start with `cardano-simple` and migrate to `kustomize-carnado` later:

1. **Start Simple**: Deploy `cardano-simple` to learn the basics
2. **Understand Components**: Learn how Cardano node and db-sync work
3. **Identify Needs**: Determine what additional features you need
4. **Gradual Migration**: Move to `kustomize-carnado` when you need more features

## Resource Requirements

### cardano-simple
- **Minimum**: 8GB RAM, 150GB storage, 2 CPU cores
- **Recommended**: 16GB RAM, 200GB storage, 4 CPU cores
- **Components**: 3 pods (node, db-sync, postgres)

### kustomize-carnado  
- **Minimum**: 16GB RAM, 300GB storage, 4 CPU cores
- **Recommended**: 32GB RAM, 500GB storage, 8 CPU cores
- **Components**: 5+ pods (node, db-sync, postgres, monitoring, etc.)

## Feature Matrix

| Feature | cardano-simple | kustomize-carnado |
|---------|:--------------:|:-----------------:|
| Cardano Node | ✅ | ✅ |
| Database Sync | ✅ | ✅ |
| PostgreSQL | ✅ | ✅ |
| Mithril Fast Sync | ✅ | ✅ |
| Multiple Networks | ❌ | ✅ |
| Network Patches | ❌ | ✅ |
| RBAC Security | ❌ | ✅ |
| Monitoring | ❌ | ✅ |
| High Availability | ❌ | ✅ |
| Custom Resources | ❌ | ✅ |
| Helm Charts | ❌ | ✅ |
| Ingress Support | ❌ | ✅ |
| Auto-scaling | ❌ | ✅ |

## Getting Started

### For Beginners:
```bash
# Clone and deploy cardano-simple
cd cardano-simple/
./deploy.sh
./check-status.sh
```

### For Advanced Users:
```bash
# Deploy kustomize-carnado
kubectl apply -k kustomize-carnado/overlays/testnet-preprod/
./validate.sh
```

## Support and Documentation

### cardano-simple
- **README.md**: Complete beginner guide
- **TROUBLESHOOTING.md**: Common issues and solutions
- **Built-in help**: Status checker and deployment script

### kustomize-carnado
- **README.md**: Technical documentation
- **Modular docs**: Component-specific documentation
- **Advanced troubleshooting**: Kubernetes and Cardano expertise required

## Conclusion

Both deployments provide working Cardano infrastructure with Mithril fast sync. Choose based on your experience level, requirements, and long-term goals:

- **Start with cardano-simple** if you're new to Cardano or want quick results
- **Use kustomize-carnado** if you need production features and have the expertise

Remember: You can always start simple and upgrade later as your needs grow!
