apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    io.kompose.service: cardano-node
  name: cardano-node
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: cardano-node
  serviceName: cardano-node
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        io.kompose.service: cardano-node
    spec:
      initContainers:
      - name: remove-lost-n-found
        image: busybox
        imagePullPolicy: IfNotPresent
        command: ["sh", "-c", "rm -rf /data/db/lost+found"]
        resources: {}
        volumeMounts:
        - mountPath: /data/db
          name: node-db
      - name: restore-from-csnapshotio
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        command: ["bash", "-x", "/configmap/initContainer-entrypoint"]
        env:
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: NETWORK
          value: mainnet
        - name: CNODE_DB_PATH
          value: /data
        resources: {}
        volumeMounts:
        - name: cardano-node-configmap
          mountPath: /configmap
        - mountPath: /data/db
          name: node-db
      containers:
      - name: cardano-node
        command: ["bash", "-c", "export HOME=/nonexistent; source $HOME/.baids/baids && set -x; ${NETWORK}-cnode-run-as-${CNODE_ROLE} +RTS -N2 --disable-delayed-os-memory-return -T -I0 -A16m"]
        env:
        - name: NETWORK
          value: iohk-mn
        - name: CNODE_HOST_ADDR
          value: 0.0.0.0
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: CNODE_DB_PATH
          value: /data/db
        - name: CNODE_TOPOLOGY_FILE
          value: /configmap/topology.json
        - name: CNODE_CONFIG_FILE
          value: /configmap/config.json
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data/db
          name: node-db
        - mountPath: /ipc
          name: node-ipc
        - mountPath: /configmap
          name: cardano-node-configmap
      - name: chisel-server
        image: jpillora/chisel:1.7
        env:
        - name: PORT
          value: "40000"
        command: ["sh", "-c", "/app/chisel server -v --authfile /common-env/chisel-auth-file -p ${PORT:-40000}"]
        ports:
        - containerPort: 40000
        volumeMounts:
        - name: common-env
          mountPath: /common-env
          readOnly: true
      - name: socat-tcp-server
        image: alpine/socat
        env:
        - name: PORT
          value: "30000"
        command: ["sh", "-c", "socat TCP-LISTEN:${PORT},fork UNIX-CLIENT:/ipc/node.socket,ignoreeof"]
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
        ports:
        - containerPort: 30000
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-db
        persistentVolumeClaim:
          claimName: node-db
      - name: node-ipc
        emptyDir: {}
      - name: common-env
        configMap:
          name: common-env
      - name: cardano-node-configmap
        configMap:
          name: cardano-node

  volumeClaimTemplates:
  - metadata:
      name: node-db
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 8Gi
