apiVersion: v1
automountServiceAccountToken: true
kind: ServiceAccount
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-deploy-sql
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-deploy-sql
rules:
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - delete
  - get
  - watch
  - list
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: pod-reader
rules:
- apiGroups:
  - ""
  resources:
  - pods
  - pods/log
  verbs:
  - get
  - watch
  - list
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-deploy-sql
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: koios-deploy-sql
subjects:
- kind: ServiceAccount
  name: koios-deploy-sql
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: pod-reader
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: pod-reader
subjects:
- kind: ServiceAccount
  name: default
---
apiVersion: v1
data:
  override.conf: max_locks_per_transaction = 256
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 16.0.0
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-postgresql-extended-configuration
  namespace: default
---
apiVersion: v1
data:
  pre-stop.sh: "#!/bin/bash\nset -o errexit\nset -o pipefail\nset -o nounset\n\n#
    Debug section\nexec 3>&1\nexec 4>&2\n\n# Process input parameters\nMIN_DELAY_AFTER_PG_STOP_SECONDS=$1\n\n#
    Load Libraries\n. /opt/bitnami/scripts/liblog.sh\n. /opt/bitnami/scripts/libpostgresql.sh\n.
    /opt/bitnami/scripts/librepmgr.sh\n\n# Load PostgreSQL & repmgr environment variables\n.
    /opt/bitnami/scripts/postgresql-env.sh\n\n# Auxiliary functions\nis_new_primary_ready()
    {\n    return_value=1\n    currenty_primary_node=\"$(repmgr_get_primary_node)\"\n
    \   currenty_primary_host=\"$(echo $currenty_primary_node | awk '{print $1}')\"\n\n
    \   info \"$currenty_primary_host != $REPMGR_NODE_NETWORK_NAME\"\n    if [[ $(echo
    $currenty_primary_node | wc -w) -eq 2 ]] && [[ \"$currenty_primary_host\" != \"$REPMGR_NODE_NETWORK_NAME\"
    ]]; then\n        info \"New primary detected, leaving the cluster...\"\n        return_value=0\n
    \   else\n        info \"Waiting for a new primary to be available...\"\n    fi\n
    \   return $return_value\n}\n\nexport MODULE=\"pre-stop-hook\"\n\nif [[ \"${BITNAMI_DEBUG}\"
    == \"true\" ]]; then\n    info \"Bash debug is on\"\nelse\n    info \"Bash debug
    is off\"\n    exec 1>/dev/null\n    exec 2>/dev/null\nfi\n\npostgresql_enable_nss_wrapper\n\n#
    Prepare env vars for managing roles\nreadarray -t primary_node < <(repmgr_get_upstream_node)\nprimary_host=\"${primary_node[0]}\"\n\n#
    Stop postgresql for graceful exit.\nPG_STOP_TIME=$EPOCHSECONDS\npostgresql_stop\n\nif
    [[ -z \"$primary_host\" ]] || [[ \"$primary_host\" == \"$REPMGR_NODE_NETWORK_NAME\"
    ]]; then\n    info \"Primary node need to wait for a new primary node before leaving
    the cluster\"\n    retry_while is_new_primary_ready 10 5\nelse\n    info \"Standby
    node doesn't need to wait for a new primary switchover. Leaving the cluster\"\nfi\n\n#
    Make sure pre-stop hook waits at least 25 seconds after stop of PG to make sure
    PGPOOL detects node is down.\n# default terminationGracePeriodSeconds=30 seconds\nPG_STOP_DURATION=$(($EPOCHSECONDS
    - $PG_STOP_TIME))\nif (( $PG_STOP_DURATION < $MIN_DELAY_AFTER_PG_STOP_SECONDS
    )); then\n    WAIT_TO_PG_POOL_TIME=$(($MIN_DELAY_AFTER_PG_STOP_SECONDS - $PG_STOP_DURATION))
    \n    info \"PG stopped including primary switchover in $PG_STOP_DURATION. Waiting
    additional $WAIT_TO_PG_POOL_TIME seconds for PG pool\"\n    sleep $WAIT_TO_PG_POOL_TIME\nfi"
  readiness-probe.sh: "#!/bin/bash\nset -o errexit\nset -o pipefail\nset -o nounset\n\n#
    Debug section\nexec 3>&1\nexec 4>&2\n\n# Load Libraries\n. /opt/bitnami/scripts/liblog.sh\n.
    /opt/bitnami/scripts/libpostgresql.sh\n\n# Load PostgreSQL & repmgr environment
    variables\n. /opt/bitnami/scripts/postgresql-env.sh\n\n# Process input parameters\nMIN_DELAY_AFTER_POD_READY_FIRST_TIME=$1\nTMP_FIRST_READY_FILE_TS=\"/tmp/ts-first-ready.mark\"\nTMP_DELAY_APPLIED_FILE=\"/tmp/delay-applied.mark\"\n\n\nDB_CHECK_RESULT=$(echo
    \"SELECT 1\" | postgresql_execute \"$POSTGRESQL_DATABASE\" \"$POSTGRESQL_USERNAME\"
    \"$POSTGRESQL_PASSWORD\" \"-h 127.0.0.1 -tA\" || \"command failed\")\nif [[ \"$DB_CHECK_RESULT\"
    == \"1\" ]]; then\n  if [[ ! -f \"$TMP_DELAY_APPLIED_FILE\" ]]; then\n    # DB
    up, but initial readiness delay not applied\n    if [[ -f \"$TMP_FIRST_READY_FILE_TS\"
    ]]; then\n      # calculate delay from the first readiness success\n      FIRST_READY_TS=$(cat
    $TMP_FIRST_READY_FILE_TS)\n      CURRENT_DELAY_SECONDS=$(($EPOCHSECONDS - $FIRST_READY_TS))\n
    \     if (( $CURRENT_DELAY_SECONDS > $MIN_DELAY_AFTER_POD_READY_FIRST_TIME ));
    then\n        # minimal delay of the first readiness state passed - report success
    and mark delay as applied\n        touch \"$TMP_DELAY_APPLIED_FILE\"      \n      else\n
    \       # minimal delay of the first readiness state not reached yet - report
    failure\n        return 1\n      fi\n    else\n      # first ever readiness test
    success - store timestamp and report failure\n      echo $EPOCHSECONDS > $TMP_FIRST_READY_FILE_TS\n
    \     return 1\n    fi\n  fi\nelse\n  # DB test failed - report failure\n  return
    1\nfi"
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 16.0.0
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-postgresql-hooks-scripts
  namespace: default
---
apiVersion: v1
data:
  blockfrost-indexes.sql: |
    CREATE INDEX IF NOT EXISTS bf_idx_block_hash_encoded ON block USING HASH (encode(hash, 'hex'));
    CREATE INDEX IF NOT EXISTS bf_idx_datum_hash ON datum USING HASH (encode(hash, 'hex'));
    CREATE INDEX IF NOT EXISTS bf_idx_multi_asset_policy ON multi_asset USING HASH (encode(policy, 'hex'));
    CREATE INDEX IF NOT EXISTS bf_idx_multi_asset_policy_name ON multi_asset USING HASH ((encode(policy, 'hex') || encode(name, 'hex')));
    CREATE INDEX IF NOT EXISTS bf_idx_pool_hash_view ON pool_hash USING HASH (view);
    CREATE INDEX IF NOT EXISTS bf_idx_redeemer_data_hash ON redeemer_data USING HASH (encode(hash, 'hex'));
    CREATE INDEX IF NOT EXISTS bf_idx_scripts_hash ON script USING HASH (encode(hash, 'hex'));
    CREATE INDEX IF NOT EXISTS bf_idx_tx_hash ON tx USING HASH (encode(hash, 'hex'));
    CREATE UNIQUE INDEX IF NOT EXISTS bf_u_idx_epoch_stake_epoch_and_id ON epoch_stake (epoch_no, id);
    CREATE INDEX IF NOT EXISTS bf_idx_reference_tx_in_tx_in_id ON reference_tx_in (tx_in_id);
    CREATE INDEX IF NOT EXISTS bf_idx_collateral_tx_in_tx_in_id ON collateral_tx_in (tx_in_id);
  initContainer-entrypoint: |
    export PGCONNECT_TIMEOUT=10
    while [ -z "$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB} | grep epoch)" ]
    do
      sleep 1
    done
    psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f /configmap/blockfrost-indexes.sql ${POSTGRES_DB}
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: blockfrost-configmap
---
apiVersion: v1
data:
  cardano-db-sync-config.json: |
    {
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "NetworkName": "mainnet",
      "NodeConfigFile": "/opt/cardano/cnode/files/cardano-node-config.json",
      "PrometheusPort": 8080,
      "RequiresNetworkMagic": "RequiresNoMagic",
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "cfokey": {
          "value": "Release-1.0.0"
        },
        "mapBackends": {},
        "mapSeverity": {
          "db-sync-node": "Info",
          "db-sync-node.Mux": "Error",
          "db-sync-node.Subscription": "Error"
        },
        "mapSubtrace": {
          "#ekgview": {
            "contents": [
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": ".monoclock.basic.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": "diff.RTS.cpuNs.timed.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "#ekgview.#aggregation.cardano.epoch-validation.benchmark",
                  "tag": "StartsWith"
                },
                [
                  {
                    "contents": "diff.RTS.gcNum.timed.",
                    "tag": "Contains"
                  }
                ]
              ]
            ],
            "subtrace": "FilterTrace"
          },
          "#messagecounters.aggregation": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.ekgview": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.katip": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.monitoring": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.switchboard": {
            "subtrace": "NoTrace"
          },
          "benchmark": {
            "contents": [
              "GhcRtsStats",
              "MonotonicClock"
            ],
            "subtrace": "ObservableTrace"
          },
          "cardano.epoch-validation.utxo-stats": {
            "subtrace": "NoTrace"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 5000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "AggregationBK",
        "KatipBK"
      ],
      "setupScribes": [
        {
          "scFormat": "ScText",
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
  create-db-ro-user-entrypoint: |
    export PGCONNECT_TIMEOUT=10
    while [ -z "$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB} | grep epoch)" ]
    do
      sleep 1
    done
    envsubst < /configmap/create-ro-user.sql.tpl > /tmp/create-ro-user.sql
    psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f /tmp/create-ro-user.sql ${POSTGRES_DB}
    psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f /configmap/extra.sql ${POSTGRES_DB}
  create-ro-user.sql.tpl: |
    CREATE ROLE ${POSTGRES_USER_RO};
    GRANT CONNECT ON DATABASE ${POSTGRES_DB} TO ${POSTGRES_USER_RO};
    GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${POSTGRES_USER_RO};
    GRANT USAGE ON SCHEMA public TO ${POSTGRES_USER_RO};
    ALTER ROLE ${POSTGRES_USER_RO} WITH login;
    ALTER USER ${POSTGRES_USER_RO} WITH PASSWORD '${POSTGRES_PASSWORD_RO}';
    GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA PUBLIC TO ${POSTGRES_USER_RO};
  entrypoint: |
    set +x
    export PGPASSFILE=/tmp/pgpassfile
    echo "${POSTGRES_HOST}:${POSTGRES_PORT}:${POSTGRES_DB}:${POSTGRES_USER}:${POSTGRES_PASSWORD}" > ${PGPASSFILE}
    chmod 0600 ${PGPASSFILE}
    set -x

    CARDANO_DB_SYNC_BINARY=$(find /nix/store/*cardano-db-sync-exe-cardano-db-sync*/bin -name cardano-db-sync)
    CARDANO_DB_SYNC_SCHEMA_DIR=$(find /nix/store/*cardano-db-sync-schema*/ -name schema -type d)
    if [ "${DISABLE_EPOCH}" == "true" ]; then DISABLE_EPOCH="--disable-epoch"; else DISABLE_EPOCH=""; fi
    if [ "${DISABLE_CACHE}" == "true" ]; then DISABLE_CACHE="--disable-cache"; else DISABLE_CACHE=""; fi
    if [ "${DISABLE_LEDGER}" == "true" ]; then DISABLE_LEDGER="--disable-ledger"; else DISABLE_LEDGER=""; fi
    exec ${CARDANO_DB_SYNC_BINARY} \
      ${DISABLE_EPOCH} \
      ${DISABLE_CACHE} \
      ${DISABLE_LEDGER} \
      --config /configmap/cardano-db-sync-config.json \
      --socket-path "$CARDANO_NODE_SOCKET_PATH" \
      --schema-dir "${CARDANO_DB_SYNC_SCHEMA_DIR}" \
      --state-dir "${CARDANO_DB_SYNC_STATE_DIR}"
  extra.sql: "CREATE OR REPLACE FUNCTION get_tx_history_for_addresses(data json) RETURNS
    TABLE (tx_hash text, block word31type, tx_timestamp timestamp) AS $$\nDECLARE\n
    \ addresses text[];\nBEGIN\n  addresses := (SELECT array_agg(replace(rec::text,
    '\"', ''))\n                FROM json_array_elements(data->'addresses') rec);\n
    \ RETURN QUERY (SELECT trim(txs.hash, '\\\\\\\\x') , txs.block_no, txs.time from
    (\n    SELECT\n      tx.id, tx.hash::text, block.block_no, block.hash::text as
    blockHash, block.time, tx.block_index\n      FROM block\n      INNER JOIN tx ON
    block.id = tx.block_id\n      INNER JOIN tx_out ON tx.id = tx_out.tx_id\n      WHERE
    tx_out.address = ANY(addresses)\n    UNION\n      SELECT DISTINCT\n        tx.id,
    tx.hash::text, block.block_no, block.hash::text as blockHash, block.time, tx.block_index\n
    \       FROM block\n        INNER JOIN tx ON block.id = tx.block_id\n        INNER
    JOIN tx_in ON tx.id = tx_in.tx_in_id\n        INNER JOIN tx_out ON (tx_in.tx_out_id
    = tx_out.tx_id) AND (tx_in.tx_out_index = tx_out.index)\n        WHERE tx_out.address
    = ANY(addresses)\n        ORDER BY time DESC\n  ) AS txs);\nEND; $$ LANGUAGE PLPGSQL
    IMMUTABLE;\n\nCREATE OR REPLACE FUNCTION get_eoe_balance_for_addresses(data json)
    RETURNS TABLE (balance numeric, address character varying) AS $$\nDECLARE\n  addresses
    text[];\n  epoch int;\nBEGIN\n  addresses := (SELECT array_agg(replace(rec::text,
    '\"', ''))\n                FROM json_array_elements(data->'addresses') rec);\n
    \ SELECT json_extract_path_text(data, 'epoch') INTO epoch AS tmp;\n  RETURN QUERY
    (SELECT SUM(utxo_view.value), utxo_view.address FROM utxo_view\n    INNER JOIN
    tx ON tx.id = utxo_view.tx_id\n    INNER JOIN block ON block.id = tx.block_id\n
    \   WHERE utxo_view.address = ANY(addresses)\n    AND block.slot_no <= (select
    get_last_slot_for_epoch(epoch))\n    GROUP BY utxo_view.address);\nEND; $$ LANGUAGE
    PLPGSQL IMMUTABLE;\n\nCREATE FUNCTION get_metadata(metadatum word64type default
    0, epochs int[] default null) RETURNS TABLE (epoch word31type, data jsonb) AS
    $$\nBEGIN\n  IF epochs IS NOT NULL THEN\n    RETURN QUERY (select block.epoch_no,
    json as epoch from tx_metadata\n                  inner join tx on tx_metadata.tx_id
    = tx.id\n                  inner join block on tx.block_id = block.id\n                  where
    key = metadatum and epoch_no = ANY(epochs)\n                  order by epoch_no);\n
    \ ELSE\n    RETURN QUERY (select block.epoch_no, json as epoch from tx_metadata\n
    \                 inner join tx on tx_metadata.tx_id = tx.id\n                  inner
    join block on tx.block_id = block.id\n                  where key = metadatum\n
    \                 order by epoch_no);\n  END IF;\nEND; $$ LANGUAGE PLPGSQL IMMUTABLE;\n\nCREATE
    OR REPLACE FUNCTION get_pools_dbsync_hash_id(_pool_bech32_ids text[] DEFAULT null)
    RETURNS TABLE (pool_dbsync_hash_id bigint, pool_bech32_id character varying, vrf_key_hash
    character varying) AS $$\nselect distinct pool_hash.id, pool_hash.view, encode(pool_update.vrf_key_hash,
    'hex') from pool_update \n              inner join pool_hash on pool_update.hash_id
    = pool_hash.id\n              where pool_update.registered_tx_id in (select max(pool_update.registered_tx_id)
    from pool_update group by hash_id)\n              and not exists\n              (
    select * from pool_retire where pool_retire.hash_id = pool_update.hash_id\n                and
    pool_retire.retiring_epoch <= (select max (epoch_no) from block)\n              )
    \n              AND CASE\n              WHEN _pool_bech32_ids IS NULL THEN true\n
    \             WHEN _pool_bech32_ids IS NOT NULL THEN pool_hash.view = ANY(SELECT
    UNNEST(_pool_bech32_ids))\n              END;\n$$ LANGUAGE SQL IMMUTABLE;\n"
  initContainer-restore-snapshot-entrypoint: |
    function finish-socat {
      pkill -f socat && sleep 1
      rm -f ${CARDANO_NODE_SOCKET_PATH}
    }

    trap finish-socat EXIT

    export PGCONNECT_TIMEOUT=10

    case "${NETWORK}" in
      "mainnet") S3_BUCKET_URI=${DB_SYNC_SNAPSHOT_MAINNET_S3_BUCKET_URI};;
      "testnet") S3_BUCKET_URI=${DB_SYNC_SNAPSHOT_TESTNET_S3_BUCKET_URI};;
    esac

    if [ "${RESTORE_SNAPSHOT}" == "true" ]
    then
      until psql -P pager=off -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB}
      do
        sleep 1
      done
      if [ -z "$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -c '\dt' ${POSTGRES_DB} | grep epoch)" ]
      then
        cd /aux-data-dir

        read -d "\n" DB_SYNC_SNAPSHOT DB_SYNC_SNAPSHOT_SHA256_SUM <<<$(aws s3 ls --no-sign-request ${S3_BUCKET_URI} | awk '{print $NF}' | sort | tail -n2)

        aws s3 cp --no-sign-request ${S3_BUCKET_URI}${DB_SYNC_SNAPSHOT} snapshot.tgz
        aws s3 cp --no-sign-request ${S3_BUCKET_URI}${DB_SYNC_SNAPSHOT_SHA256_SUM} snapshot.tgz.sha256sum.tmp

        echo $(awk '{print $1}' snapshot.tgz.sha256sum.tmp) snapshot.tgz > snapshot.tgz.sha256sum
        sha256sum --check --status < snapshot.tgz.sha256sum
        if [ $? -eq 0 ]
        then
          tar -zxf snapshot.tgz && rm -f snapshot.tgz
          psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -f *sql ${POSTGRES_DB}
          rm -f *sql
          mv *state* /db-sync-statedir
          chown -R root: /db-sync-statedir
        else
          echo "[!] sha256sum check error for ${S3_BUCKET_URI}${DB_SYNC_SNAPSHOT}"
          exit 1
        fi
      fi
    fi
    DB_BLOCK_NO=$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -tA -P pager=off -c 'select block_no from block order by id desc limit 1;' ${POSTGRES_DB})
    test -z "${DB_BLOCK_NO}" && DB_BLOCK_NO=0

    echo -n "[+] Waiting for cardano-node to become available (socat approach makes db-sync to freeze if it's not)..."
    while ! `echo > /dev/tcp/${CARDANO_NODE_SOCKET_TCP_HOST}/${CARDANO_NODE_SOCKET_TCP_PORT}`
    do
      sleep 1
      echo -n .
    done

    rm -f ${CARDANO_NODE_SOCKET_PATH}
    socat UNIX-LISTEN:${CARDANO_NODE_SOCKET_PATH},fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof &
    while ! `timeout 10 cardano-cli query tip --mainnet 2>&1 | grep -q "epoch\|NodeToClientVersionData"`
    do
      sleep 1
      echo -n .
    done

    cardano-cli query tip --mainnet
    if [ $? -eq 0 ]
    then
      MAGIC_ARG="--mainnet"
    else
      MAGIC_ARG="--testnet-magic $(cardano-cli query tip --mainnet 2>&1 | sed 's|\(.*NodeToClientVersionData.*\)unNetworkMagic = \(.*\)}}.*/=\(.*\)|\2|g' | xargs echo)"
    fi

    echo -n "[+] Waiting for cardano-node to reach tip..."
    while [[ -n $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]] && [ ${DB_BLOCK_NO} -gt $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]
    do
      sleep 60
      echo -n .
    done
  initContainer-setup-node-config-entrypoint: |
    rm -rf /config/lost+found

    cp /opt/cardano/cnode/files/config.json /config/cardano-node-config.json
    cp /opt/cardano/cnode/files/byron-genesis.json /config/
    cp /opt/cardano/cnode/files/genesis.json /config/
    cp /opt/cardano/cnode/files/alonzo-genesis.json /config/
  liveness-healthcheck: "KUBE_API_URL=https://kubernetes.default.svc/api/v1/namespaces\nKUBE_TOKEN=$(cat
    /var/run/secrets/kubernetes.io/serviceaccount/token)\nNAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)\nSINCE_SECONDS=300\ncurl
    -m 30 -sSk -H \"Authorization: Bearer $KUBE_TOKEN\" \"${KUBE_API_URL}/${NAMESPACE}/pods/cardano-db-sync-0/log?container=cardano-db-sync&sinceSeconds=${SINCE_SECONDS}\"
    > /tmp/db-sync-log-tail \nif [ $? -ne 0 ]\nthen\n  exit 0\nelse\n  grep -q \"libpq.*failed.*no
    connection to the server\" /tmp/db-sync-log-tail\n  if [ $? -eq 0 ]\n  then\n
    \   echo \"[!] Postgres connection lost, failing healthcheck...\"\n    exit 1\n
    \ fi\nfi\n"
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-db-sync-configmap
---
apiVersion: v1
data:
  initContainer-entrypoint: |
    rm -rf /config/lost+found

    CNODE_FILES="config.json byron-genesis.json genesis.json alonzo-genesis.json"

    for file in ${CNODE_FILES}
    do
      cp -a /opt/cardano/cnode/files/${file} /config
    done
  initContainer-wait-for-tip: |2

    function finish-socat {
      pkill -f socat && sleep 1
      rm -f ${CARDANO_NODE_SOCKET_PATH}
    }

    trap finish-socat EXIT

    export PGCONNECT_TIMEOUT=10

    echo -n "[+] Waiting for cardano-node to become available (socat approach makes db-sync to freeze if it's not)..."
    while ! `echo > /dev/tcp/${CARDANO_NODE_SOCKET_TCP_HOST}/${CARDANO_NODE_SOCKET_TCP_PORT}`
    do
      sleep 1
      echo -n .
    done

    rm -f ${CARDANO_NODE_SOCKET_PATH}
    socat UNIX-LISTEN:${CARDANO_NODE_SOCKET_PATH},fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof &
    while ! `timeout 10 cardano-cli query tip --mainnet 2>&1 | grep -q "epoch\|NodeToClientVersionData"`
    do
      sleep 1
      echo -n .
    done

    cardano-cli query tip --mainnet
    if [ $? -eq 0 ]
    then
      MAGIC_ARG="--mainnet"
    else
      MAGIC_ARG="--testnet-magic $(cardano-cli query tip --mainnet 2>&1 | sed 's|\(.*NodeToClientVersionData.*\)unNetworkMagic = \(.*\)}}.*/=\(.*\)|\2|g' | xargs echo)"
    fi

    DB_BLOCK_NO=$(psql -U ${POSTGRES_USER} -h ${POSTGRES_HOST} -tA -P pager=off -c 'select block_no from block order by id desc limit 1;' ${POSTGRES_DB})
    test -z "${DB_BLOCK_NO}" && DB_BLOCK_NO=0
    echo -n "[+] Waiting for cardano-node to reach tip..."
    while [[ -n $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]] && [ ${DB_BLOCK_NO} -gt $(cardano-cli query tip ${MAGIC_ARG} | jq -r .block) ]
    do
      sleep 60
      echo -n .
    done

    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)

    echo -n "[!] Waiting for db-sync to be initialized..."
    while [[ -z $(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^cardano-db-sync) ]]
    do
      echo -n .
      sleep 20
    done
  liveness-healthcheck: |
    HEALTHCHECK_TIMEOUT=5
    timeout ${HEALTHCHECK_TIMEOUT} curl -w %{http_code} -k http://localhost:3100 -H 'Accept-Encoding: gzip, deflate, br' -H 'Content-Type: application/json' -H 'Accept: application/json' -H 'Connection: keep-alive' -H 'DNT: 1' --data-binary '{"query":"fragment _Sync on Query {\n  cardanoDbMeta {\n    initialized\n    syncPercentage\n    __typename\n  }\n  __typename\n}\n\nfragment _GetTip on Query {\n  cardano {\n    tip {\n      number\n      slotNo\n      slotInEpoch\n      epochNo\n      forgedAt\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nquery PaymentAddresses {\n  ..._Sync\n  ..._GetTip\n}\n"}' | grep -iq initialized.*true
    if [ $? -ne 0 ]
    then
      exit 1
    fi
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-graphql-configmap
---
apiVersion: v1
data:
  config.json: |
    {
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 0,
      "AlonzoGenesisFile": "/opt/cardano/cnode/files/alonzo-genesis.json",
      "AlonzoGenesisHash": "7e94a15f55d1e82d10f09203fa1d40f8eede58fd8066542cf6566008068ed874",
      "ByronGenesisFile": "/opt/cardano/cnode/files/byron-genesis.json",
      "ByronGenesisHash": "96fceff972c2c06bd3bb5243c39215333be6d56aaf4823073dca31afe5038471",
      "LastKnownBlockVersion-Alt": 0,
      "LastKnownBlockVersion-Major": 4,
      "LastKnownBlockVersion-Minor": 0,
      "MaxKnownMajorProtocolVersion": 4,
      "PBftSignatureThreshold": 0.9,
      "MaxConcurrencyDeadline": 1,
      "NumCoreNodes": 1,
      "Protocol": "Cardano",
      "SocketPath": "/opt/cardano/cnode/sockets/node0.socket",
      "RequiresNetworkMagic": "RequiresMagic",
      "ShelleyGenesisFile": "/opt/cardano/cnode/files/genesis.json",
      "ShelleyGenesisHash": "849a1764f152e1b09c89c0dfdbcbdd38d711d1fec2db5dfa0f87cf2737a0eaf4",
      "SocketPath": "/opt/cardano/cnode/sockets/node0.socket",
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "TraceBlockFetchClient": true,
      "TraceBlockFetchDecisions": true,
      "TraceBlockFetchProtocol": true,
      "TraceBlockFetchProtocolSerialised": true,
      "TraceBlockFetchServer": true,
      "TraceChainDb": true,
      "TraceChainSyncBlockServer": true,
      "TraceChainSyncClient": true,
      "TraceChainSyncHeaderServer": true,
      "TraceChainSyncProtocol": true,
      "TraceDNSResolver": false,
      "TraceDNSSubscription": false,
      "TraceErrorPolicy": true,
      "TraceForge": true,
      "TraceHandshake": true,
      "TraceIpSubscription": true,
      "TraceLocalChainSyncProtocol": true,
      "TraceLocalErrorPolicy": true,
      "TraceLocalHandshake": false,
      "TraceLocalTxSubmissionProtocol": true,
      "TraceLocalTxSubmissionServer": true,
      "TraceMempool": true,
      "TraceMux": false,
      "TraceTxInbound": true,
      "TraceTxOutbound": true,
      "TraceTxSubmissionProtocol": true,
      "TracingVerbosity": "NormalVerbosity",
      "TurnOnLogMetrics": false,
      "TurnOnLogging": true,
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "hasEKG": 12788,
      "hasPrometheus": [
        "0.0.0.0",
        12798
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node.metrics.Forge": [
             "EKGViewBK"
          ],
          "cardano.node.metrics": [
            "EKGViewBK"
          ],
          "cardano.node.resources": [
            "EKGViewBK"
          ]
        },
        "mapSubtrace": {
          "cardano.node.metrics": {
            "subtrace": "Neutral"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 10000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "KatipBK",
        "EKGViewBK"
      ],
      "setupScribes": [
        {
          "scFormat": "ScText",
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
  initContainer-entrypoint: |
    if [[ ! -e /data/db/lock ]] && [[ "${RESTORE_SNAPSHOT}" == "true" ]]
    then
      curl -o - https://downloads.csnapshots.io/snapshots/${NETWORK}/$(curl -s https://downloads.csnapshots.io/snapshots/${NETWORK}/${NETWORK}-db-snapshot.json | jq -r .[].file_name ) | lz4 -c -d - | tar -x -C ${CNODE_DB_PATH}
    fi
  topology.json.passive: |
    {
       "Producers": [
          {
            "addr": "relays-new.cardano-testnet.iohkdev.io",
            "port": 3001,
            "valency": 2
          }
       ]
     }
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-node
---
apiVersion: v1
data:
  config.json: |
    {
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "GenesisHash": "d4b8de7a11d929a323373cbab6c1a9bdc931beffff11db111cf9d57356ee1937",
      "PrometheusPort": 8080,
      "RequiresNetworkMagic": "RequiresMagic",
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "cfokey": {
          "value": "Release-1.0.0"
        },
        "mapBackends": {},
        "mapSeverity": {
          "db-sync-node": "Info",
          "db-sync-node.Mux": "Error",
          "db-sync-node.Subscription": "Error"
        },
        "mapSubtrace": {
          "#ekgview": {
            "contents": [
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": ".monoclock.basic.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": "diff.RTS.cpuNs.timed.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "#ekgview.#aggregation.cardano.epoch-validation.benchmark",
                  "tag": "StartsWith"
                },
                [
                  {
                    "contents": "diff.RTS.gcNum.timed.",
                    "tag": "Contains"
                  }
                ]
              ]
            ],
            "subtrace": "FilterTrace"
          },
          "#messagecounters.aggregation": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.ekgview": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.katip": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.monitoring": {
            "subtrace": "NoTrace"
          },
          "#messagecounters.switchboard": {
            "subtrace": "NoTrace"
          },
          "benchmark": {
            "contents": [
              "GhcRtsStats",
              "MonotonicClock"
            ],
            "subtrace": "ObservableTrace"
          },
          "cardano.epoch-validation.utxo-stats": {
            "subtrace": "NoTrace"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 5000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "AggregationBK",
        "KatipBK"
      ],
      "setupScribes": [
        {
          "scFormat": "ScText",
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
  entrypoint: |
    CARDANO_SUBMIT_API_BINARY=$(ls -d -1 /nix/store/*cardano-submit-api-exe-cardano-submit-api*/bin/cardano-submit-api)
    NETWORK_MAGIC=$(cat /config/networkMagic)
    if [ ${NETWORK_MAGIC} -eq 764824073 ]
    then
      NETWORK_ARG="--mainnet"
    else
      NETWORK_ARG="--testnet-magic ${NETWORK_MAGIC}"
    fi

    exec ${CARDANO_SUBMIT_API_BINARY} \
      ${NETWORK_ARG} \
      --listen-address 0.0.0.0 \
      --port 8090 \
      --socket-path "${CARDANO_NODE_SOCKET_PATH}" \
      --config "${CARDANO_SUBMIT_API_CONFIG_PATH}"
  initContainer-entrypoint: |
    rm -rf /config/lost+found

    echo $(jq .networkMagic /opt/cardano/cnode/files/genesis.json) > /config/networkMagic
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-submit-api-configmap
---
apiVersion: v1
data:
  POSTGRES_DB: cexplorer
  POSTGRES_HOST: init0-postgresql-ha-postgresql
  POSTGRES_HOST_RO: init0-postgresql-ha-postgresql
  POSTGRES_HOST_RW: init0-postgresql-ha-postgresql
  POSTGRES_PORT: "5432"
  POSTGRES_USER: postgres
  POSTGRES_USER_RO: dandelion_ro
  chisel-auth-file: |
    {
      "admin:CHANGEME": [""]
    }
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: common-env
---
apiVersion: v1
data:
  active-stake-cache-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*active-stake-cache-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/active-stake-cache-update.sh
      bash -x active-stake-cache-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-active-stake-cache-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  asset-info-cache-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*asset-info-cache-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/asset-info-cache-update.sh
      bash -x asset-info-cache-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-asset-info-cache-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  asset-registry-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*asset-registry-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/asset-registry-update.sh
      bash -x asset-registry-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-asset-registry-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  epoch-info-cache-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*epoch-info-cache-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/epoch-info-cache-update.sh
      bash -x epoch-info-cache-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-epoch-info-cache-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  grant-permissions.sql.tpl: |
    GRANT SELECT ON ALL TABLES IN SCHEMA ${PGRST_DB_SCHEMA} TO ${POSTGRES_USER_RO};
    GRANT USAGE ON SCHEMA ${PGRST_DB_SCHEMA} TO ${POSTGRES_USER_RO};
    GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA ${PGRST_DB_SCHEMA} TO ${POSTGRES_USER_RO};
  initContainer-entrypoint: |
    export PGCONNECT_TIMEOUT=10
    while [ -z "$(psql -tA -P pager=off -c 'SELECT schema_name FROM information_schema.schemata' | grep ^${PGRST_DB_SCHEMA}$)" ]
    do
      sleep 1
    done
    envsubst < /configmap/grant-permissions.sql.tpl > /tmp/grant-permissions.sql
    psql -f /tmp/grant-permissions.sql
  koios-deploy-sql-entrypoint: "function init {\n  \n  apt update -qq && apt install
    -y postgresql-client\n\n  export KUBE_API_URL=\"https://kubernetes.default.svc/api/v1/namespaces\"\n
    \ KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\n  NAMESPACE=$(cat
    /var/run/secrets/kubernetes.io/serviceaccount/namespace)\n\n\n  touch /tmp/.pgpass
    && export PGPASSFILE=/tmp/.pgpass\n  export CONFIG=${CNODE_CONFIG_FILE}\n  export
    BRANCH=${KOIOS_BRANCH}\n  export G_ACCOUNT=cardano-community\n  #export G_ACCOUNT=repsistance\n
    \ export URL_RAW=\"https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${BRANCH}\"\n
    \ export DB_SCRIPTS_URL=\"${URL_RAW}/scripts/grest-helper-scripts/db-scripts\"\n\n
    \ export CURL_TIMEOUT=20\n  export PGCONNECT_TIMEOUT=10\n}\n\nexport CONFIG=${CNODE_CONFIG_FILE}\n.
    /opt/cardano/cnode/scripts/env offline\ninit\nset +e\n\ncurl -LO https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/scripts/grest-helper-scripts/setup-grest.sh\n\nsed
    -i '/.*# Execution.*/,$d' setup-grest.sh\nsed -i \"s/^SGVERSION=.*/SGVERSION=${KOIOS_VERSION}/g\"
    setup-grest.sh\n\nsource setup-grest.sh\n\nfunction setup_cron_jobs {\n  return
    0\n}\n\necho -n \"[!] Waiting for db-sync to be initialized...\"\nwhile [[ -z
    $(curl -m 30 -sSk -H \"Authorization: Bearer $KUBE_TOKEN\" \"${KUBE_API_URL}/${NAMESPACE}/pods\"
    | jq -r '.items[] | select(.status.phase==\"Running\") | .metadata.name' | grep
    ^cardano-db-sync) ]]\ndo\n  echo -n .\n  sleep 20\ndone\n\necho -n \"[!] Waiting
    for db to reach Mary era if needed...\"\nwhile [[ \"$(psql -qtAX -d ${PGDATABASE}
    -c 'SELECT protocol_major FROM public.param_proposal WHERE protocol_major >= 4
    ORDER BY protocol_major DESC LIMIT 1' 2>/dev/null)\" == \"\" ]]\ndo\n  echo -n
    .\n  sleep 20\ndone\n\nexport FORCE_OVERWRITE='Y'\nexport RESET_GREST='Y'\nsetup_db_basics\ndeploy_query_updates\nupdate_grest_version\n\nPOSTGREST_POD=$(curl
    -m 30 -sSk -H \"Authorization: Bearer $KUBE_TOKEN\" \"${KUBE_API_URL}/${NAMESPACE}/pods\"
    | jq -r .items[].metadata.name | grep ^koios-api)\ncurl -m 30 -sSk -X DELETE -H
    \"Authorization: Bearer $KUBE_TOKEN\" \"${KUBE_API_URL}/${NAMESPACE}/pods/${POSTGREST_POD}\"\n"
  pool-history-cache-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*pool-history-cache-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/pool-history-cache-update.sh
      bash -x pool-history-cache-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-pool-history-cache-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  populate-next-epoch-nonce: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*populate-next-epoch-nonce | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/populate-next-epoch-nonce.sh
      bash -x populate-next-epoch-nonce.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-populate-next-epoch-nonce.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  stake-distribution-new-accounts-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*stake-distribution-new-accounts-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/stake-distribution-new-accounts-update.sh
      bash -x stake-distribution-new-accounts-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-stake-distribution-new-accounts-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  stake-distribution-update: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*stake-distribution-update | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/stake-distribution-update.sh
      bash -x stake-distribution-update.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-stake-distribution-update.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
  stake-snapshot-cache: |
    export KUBE_API_URL="https://kubernetes.default.svc/api/v1/namespaces"
    KUBE_TOKEN=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)
    NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
    ALREADY_RUNNING=$(curl -m 30 -sSk -H "Authorization: Bearer $KUBE_TOKEN" "${KUBE_API_URL}/${NAMESPACE}/pods" | jq -r '.items[] | select(.status.phase=="Running") | .metadata.name' | grep ^koios.*stake-snapshot-cache | grep -v ${MY_POD_NAME})
    if [ -z "${ALREADY_RUNNING}" ]
    then
      bash -x /configmap/initContainer-entrypoint
      curl -sLO  https://raw.githubusercontent.com/${KOIOS_REPOSITORY}/${KOIOS_BRANCH}/files/grest/cron/jobs/stake-snapshot-cache.sh
      bash -x stake-snapshot-cache.sh
    else
      echo "[!] Previous cronjob job already running, skipping..."
    fi

    echo "[!] Cleaning up possibly stuck 'Terminating' pods..."
    kubectl get pods -n ${NAMESPACE} | grep ^koios-stake-snapshot-cache.*Terminating | awk '{print $1}' | while read pod
    do
      kubectl patch pod -n ${NAMESPACE} \
        --type json \
        --patch '[ { "op": "remove", "path": "/metadata/finalizers" } ]' \
        $pod
    done
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-configmap
---
apiVersion: v1
data:
  initContainer-entrypoint: |
    rm -rf /config/lost+found

    CNODE_FILES="config.json byron-genesis.json genesis.json alonzo-genesis.json"

    for file in ${CNODE_FILES}
    do
      cp -a /opt/cardano/cnode/files/${file} /config
    done
kind: ConfigMap
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: ogmios-configmap
---
apiVersion: v1
data:
  admin-password: WUh3N1RaWHlUTA==
kind: Secret
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: pgpool
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 4.4.2
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-pgpool
  namespace: default
type: Opaque
---
apiVersion: v1
data:
  passwords: cjRuZDBtMXozZHAwc3Q=
  usernames: ZGFuZGVsaW9uX3Jv
kind: Secret
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: pgpool
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 4.4.2
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-pgpool-custom-users
  namespace: default
type: Opaque
---
apiVersion: v1
data:
  password: aVQ4YWtSZVVmRg==
  repmgr-password: Z3FwQ2lnWGtocQ==
kind: Secret
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 16.0.0
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-postgresql
  namespace: default
type: Opaque
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: pgpool
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 4.4.2
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-pgpool
  namespace: default
spec:
  ports:
  - name: postgresql
    nodePort: null
    port: 5432
    protocol: TCP
    targetPort: postgresql
  selector:
    app.kubernetes.io/component: pgpool
    app.kubernetes.io/instance: init0
    app.kubernetes.io/name: postgresql-ha
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 16.0.0
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-postgresql
  namespace: default
spec:
  ports:
  - name: postgresql
    port: 5432
    protocol: TCP
    targetPort: postgresql
  selector:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/name: postgresql-ha
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
    role: data
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 16.0.0
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-postgresql-headless
  namespace: default
spec:
  clusterIP: None
  ports:
  - name: postgresql
    port: 5432
    protocol: TCP
    targetPort: postgresql
  publishNotReadyAddresses: false
  selector:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/name: postgresql-ha
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
    role: data
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: blockfrost
    project_name: dandelion
  name: blockfrost
spec:
  ports:
  - name: blockfrost
    port: 3000
    targetPort: 3000
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: blockfrost
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-explorer-api
    project_name: dandelion
  name: cardano-explorer-api
spec:
  ports:
  - name: "8101"
    port: 8101
    targetPort: 8100
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-explorer-api
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-graphql
    project_name: dandelion
  name: cardano-graphql
spec:
  ports:
  - name: "3100"
    port: 3100
    targetPort: 3100
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-graphql
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-node
    project_name: dandelion
  name: cardano-node-headless
spec:
  clusterIP: None
  ports:
  - name: socat-tcp-server
    port: 30000
    protocol: TCP
    targetPort: 30000
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-node
    project_name: dandelion
  sessionAffinity: ClientIP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-rosetta
    project_name: dandelion
  name: cardano-rosetta
spec:
  ports:
  - name: "8080"
    port: 8080
    targetPort: 8080
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-rosetta
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: kompose convert -c --controller deployment --volumes persistentVolumeClaim
    kompose.version: 1.21.0 (992df58d8)
    note: free-apis!
  creationTimestamp: null
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-submit-api
    project_name: dandelion
  name: cardano-submit-api
spec:
  ports:
  - name: "8091"
    port: 8091
    targetPort: 8090
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-submit-api
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-node
    project_name: dandelion
  name: chisel-server
spec:
  ports:
  - name: chisel-server
    port: 40000
    targetPort: 40000
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-node
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: hasura
    project_name: dandelion
  name: hasura-headless
spec:
  clusterIP: None
  ports:
  - name: hasura
    port: 8080
    protocol: TCP
    targetPort: 8080
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: hasura
    project_name: dandelion
  sessionAffinity: ClientIP
  type: ClusterIP
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: koios-api
    project_name: dandelion
  name: koios-api
spec:
  ports:
  - name: "3000"
    port: 3000
    targetPort: 3000
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: koios-api
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: ogmios
    project_name: dandelion
  name: ogmios
spec:
  ports:
  - name: "1337"
    port: 1337
    targetPort: 1337
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: ogmios
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: postgrest
    project_name: dandelion
  name: postgrest
spec:
  ports:
  - name: "3000"
    port: 3000
    targetPort: 3000
  selector:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: postgrest
    project_name: dandelion
status:
  loadBalancer: {}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: pgpool
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 4.4.2
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
  name: init0-postgresql-ha-pgpool
  namespace: default
spec:
  replicas: 0
  selector:
    matchLabels:
      app.kubernetes.io/component: pgpool
      app.kubernetes.io/instance: init0
      app.kubernetes.io/name: postgresql-ha
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        app.kubernetes.io/component: pgpool
        app.kubernetes.io/instance: init0
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: postgresql-ha
        app.kubernetes.io/version: 4.4.2
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        helm.sh/chart: postgresql-ha-11.9.8
        project_name: dandelion
    spec:
      affinity:
        nodeAffinity: null
        podAffinity: null
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchLabels:
                  app.kubernetes.io/component: pgpool
                  app.kubernetes.io/instance: init0
                  app.kubernetes.io/name: postgresql-ha
                  cardano_network: testnet
                  cardano_node_type: haskell
                  environment: production
                  project_name: dandelion
              topologyKey: kubernetes.io/hostname
            weight: 1
      containers:
      - env:
        - name: BITNAMI_DEBUG
          value: "false"
        - name: PGPOOL_POSTGRES_CUSTOM_USERS
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGPOOL_POSTGRES_CUSTOM_PASSWORDS
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGPOOL_BACKEND_NODES
          value: 0:init0-postgresql-ha-postgresql-0.init0-postgresql-ha-postgresql-headless:5432,
        - name: PGPOOL_SR_CHECK_USER
          value: repmgr
        - name: PGPOOL_SR_CHECK_PASSWORD
          valueFrom:
            secretKeyRef:
              key: repmgr-password
              name: init0-postgresql-ha-postgresql
        - name: PGPOOL_SR_CHECK_DATABASE
          value: postgres
        - name: PGPOOL_ENABLE_LDAP
          value: "no"
        - name: PGPOOL_POSTGRES_USERNAME
          value: postgres
        - name: PGPOOL_POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: PGPOOL_ADMIN_USERNAME
          value: admin
        - name: PGPOOL_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              key: admin-password
              name: init0-postgresql-ha-pgpool
        - name: PGPOOL_AUTHENTICATION_METHOD
          value: scram-sha-256
        - name: PGPOOL_ENABLE_LOAD_BALANCING
          value: "yes"
        - name: PGPOOL_DISABLE_LOAD_BALANCE_ON_WRITE
          value: transaction
        - name: PGPOOL_ENABLE_LOG_CONNECTIONS
          value: "no"
        - name: PGPOOL_ENABLE_LOG_HOSTNAME
          value: "yes"
        - name: PGPOOL_ENABLE_LOG_PER_NODE_STATEMENT
          value: "no"
        - name: PGPOOL_RESERVED_CONNECTIONS
          value: "1"
        - name: PGPOOL_CHILD_LIFE_TIME
          value: ""
        - name: PGPOOL_ENABLE_TLS
          value: "no"
        - name: PGPOOL_HEALTH_CHECK_PSQL_TIMEOUT
          value: "6"
        envFrom: null
        image: docker.io/bitnami/pgpool:4.4.2-debian-11-r5
        imagePullPolicy: IfNotPresent
        livenessProbe:
          exec:
            command:
            - /opt/bitnami/scripts/pgpool/healthcheck.sh
          failureThreshold: 5
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        name: pgpool
        ports:
        - containerPort: 5432
          name: postgresql
          protocol: TCP
        readinessProbe:
          exec:
            command:
            - bash
            - -ec
            - PGPASSWORD=${PGPOOL_POSTGRES_PASSWORD} psql -U "postgres" -d "cexplorer"
              -h /opt/bitnami/pgpool/tmp -tA -c "SELECT 1" >/dev/null
          failureThreshold: 5
          initialDelaySeconds: 5
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 5
        resources:
          limits: {}
          requests: {}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: false
          runAsGroup: 0
          runAsNonRoot: true
          runAsUser: 1001
          seccompProfile:
            type: RuntimeDefault
      securityContext:
        fsGroup: 1001
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: blockfrost
    project_name: dandelion
  name: blockfrost
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: blockfrost
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: blockfrost
        project_name: dandelion
    spec:
      containers:
      - env:
        - name: BLOCKFROST_CONFIG_NETWORK
          value: testnet
        - name: BLOCKFROST_CONFIG_DBSYNC_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: BLOCKFROST_CONFIG_DBSYNC_DATABASE
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: BLOCKFROST_CONFIG_DBSYNC_USER
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        - name: BLOCKFROST_CONFIG_SERVER_PROMETHEUS_METRICS
          value: "true"
        - name: BLOCKFROST_CONFIG_TOKEN_REGISTRY_URL
          value: http://cardano-token-registry:3042
        - name: BLOCKFROST_CONFIG_SERVER_LISTEN_ADDRESS
          value: 0.0.0.0
        image: blockfrost/backend-ryo:v1.5.0
        imagePullPolicy: IfNotPresent
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 3
        name: blockfrost
        ports:
        - containerPort: 3000
        resources: {}
        volumeMounts:
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
      initContainers:
      - command:
        - bash
        - -x
        - /configmap/initContainer-entrypoint
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        name: configure
        volumeMounts:
        - mountPath: /configmap
          name: blockfrost-configmap
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - configMap:
          name: blockfrost-configmap
        name: blockfrost-configmap
      - configMap:
          name: common-env
        name: common-env
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-explorer-api
    project_name: dandelion
  name: cardano-explorer-api
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-explorer-api
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-explorer-api
        project_name: dandelion
    spec:
      containers:
      - env:
        - name: NETWORK
          value: mainnet
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        image: inputoutput/cardano-explorer-api:3.1.2
        imagePullPolicy: IfNotPresent
        name: cardano-explorer-api
        ports:
        - containerPort: 8100
        resources: {}
        volumeMounts:
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: password
            path: POSTGRES_PASSWORD
          secretName: init0-postgresql-ha-postgresql
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-graphql
    project_name: dandelion
  name: cardano-graphql
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-graphql
      project_name: dandelion
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-graphql
        project_name: dandelion
    spec:
      containers:
      - env:
        - name: ALLOW_INTROSPECTION
          value: "true"
        - name: CACHE_ENABLED
          value: "true"
        - name: LOGGER_MIN_SEVERITY
          value: info
        - name: CARDANO_NODE_CONFIG_PATH
          value: /opt/cardano/cnode/files/config.json
        - name: HASURA_URI
          value: http://hasura-headless:8080
        - name: OGMIOS_HOST
          value: ogmios
        - name: OGMIOS_PORT
          value: "1337"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB_FILE
          value: /run/secrets/common-env/POSTGRES_DB
        - name: POSTGRES_USER_FILE
          value: /run/secrets/common-env/POSTGRES_USER
        - name: POSTGRES_PASSWORD_FILE
          value: /run/secrets/postgres-password/POSTGRES_PASSWORD
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        image: inputoutput/cardano-graphql:7.0.1
        imagePullPolicy: Always
        name: cardano-graphql
        ports:
        - containerPort: 3100
        resources: {}
        volumeMounts:
        - mountPath: /configmap
          name: cardano-graphql-configmap
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
        - mountPath: /ipc
          name: node-ipc
      - command:
        - sh
        - -c
        - rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        image: alpine/socat
        name: socat-socket-server
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      initContainers:
      - command:
        - bash
        - -x
        - /configmap/initContainer-wait-for-tip
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: repsistance/cardano-node:iohk-tn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        name: wait-for-tip
        volumeMounts:
        - mountPath: /configmap
          name: cardano-graphql-configmap
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - emptyDir: {}
        name: node-ipc
      - configMap:
          name: cardano-graphql-configmap
        name: cardano-graphql-configmap
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: password
            path: POSTGRES_PASSWORD
          secretName: init0-postgresql-ha-postgresql
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-graphql-background
    project_name: dandelion
  name: cardano-graphql-background
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-graphql-background
      project_name: dandelion
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-graphql-background
        project_name: dandelion
    spec:
      containers:
      - env:
        - name: LOGGER_MIN_SEVERITY
          value: info
        - name: HASURA_URI
          value: http://hasura-headless:8080
        - name: METADATA_SERVER_URI
          value: http://cardano-token-registry:3042
        - name: OGMIOS_HOST
          value: ogmios
        - name: OGMIOS_PORT
          value: "1337"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB_FILE
          value: /run/secrets/common-env/POSTGRES_DB
        - name: POSTGRES_USER_FILE
          value: /run/secrets/common-env/POSTGRES_USER
        - name: POSTGRES_PASSWORD_FILE
          value: /run/secrets/postgres-password/POSTGRES_PASSWORD
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        image: inputoutput/cardano-graphql-background:8.0.0-mainnet
        imagePullPolicy: Always
        name: cardano-graphql-background
        resources: {}
        volumeMounts:
        - mountPath: /configmap
          name: cardano-graphql-configmap
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
      initContainers:
      - command:
        - bash
        - -x
        - /configmap/initContainer-wait-for-tip
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: gimbalabs/cardano-db-sync-init-container:1.35.7-0
        imagePullPolicy: IfNotPresent
        name: wait-for-tip
        volumeMounts:
        - mountPath: /configmap
          name: cardano-graphql-configmap
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - emptyDir: {}
        name: node-ipc
      - configMap:
          name: cardano-graphql-configmap
        name: cardano-graphql-configmap
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: password
            path: POSTGRES_PASSWORD
          secretName: init0-postgresql-ha-postgresql
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-rosetta
    project_name: dandelion
  name: cardano-rosetta
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-rosetta
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-rosetta
        project_name: dandelion
    spec:
      containers:
      - command:
        - sh
        - -c
        - DB_CONNECTION_STRING="postgresql://${POSTGRES_USER}:${POSTGRES_PASS}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}"
          node /cardano-rosetta-server/dist/src/server/index.js
        env:
        - name: BIND_ADDRESS
          value: 0.0.0.0
        - name: PORT
          value: "8080"
        - name: LOGGER_LEVEL
          value: debug
        - name: DEFAULT_RELATIVE_TTL
          value: "1000"
        - name: NODE_ENV
          value: production
        - name: PAGE_SIZE
          value: "30"
        - name: GENESIS_SHELLEY_PATH
          value: /config/genesis/shelley.json
        - name: TOPOLOGY_FILE_PATH
          value: /config/cardano-node/topology.json
        - name: CARDANO_CLI_PATH
          value: /usr/local/bin/cardano-cli
        - name: CARDANO_NODE_PATH
          value: /usr/local/bin/cardano-node
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RO
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASS
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        image: inputoutput/cardano-rosetta:master-testnet
        imagePullPolicy: Always
        livenessProbe:
          initialDelaySeconds: 15
          periodSeconds: 15
          tcpSocket:
            port: 8080
        name: cardano-rosetta
        ports:
        - containerPort: 8080
        readinessProbe:
          initialDelaySeconds: 15
          periodSeconds: 15
          tcpSocket:
            port: 8080
        resources: {}
        volumeMounts:
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /ipc
          name: node-ipc
      - command:
        - sh
        - -c
        - rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        image: alpine/socat
        name: socat-socket-server
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - emptyDir: {}
        name: node-ipc
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: passwords
            path: POSTGREST_RO_PASSWORD
          secretName: init0-postgresql-ha-pgpool-custom-users
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-submit-api
    project_name: dandelion
  name: cardano-submit-api
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-submit-api
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-submit-api
        project_name: dandelion
    spec:
      containers:
      - command:
        - sh
        - -x
        - /configmap/entrypoint
        env:
        - name: NETWORK
          value: testnet
        image: inputoutput/cardano-submit-api:8.1.1
        imagePullPolicy: IfNotPresent
        name: cardano-submit-api
        ports:
        - containerPort: 8090
        resources: {}
        volumeMounts:
        - mountPath: /node-ipc
          name: node-ipc
        - mountPath: /configmap
          name: cardano-submit-api-configmap
        - mountPath: /config
          name: cardano-submit-api-config
      - command:
        - sh
        - -c
        - rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        image: alpine/socat
        name: socat-socket-server
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      initContainers:
      - command:
        - bash
        - /configmap/initContainer-entrypoint
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        name: configure
        volumeMounts:
        - mountPath: /config
          name: cardano-submit-api-config
        - mountPath: /configmap
          name: cardano-submit-api-configmap
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - emptyDir: {}
        name: node-ipc
      - configMap:
          name: cardano-submit-api-configmap
        name: cardano-submit-api-configmap
      - emptyDir: {}
        name: cardano-submit-api-config
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: hasura
    project_name: dandelion
  name: hasura
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: hasura
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: hasura
        project_name: dandelion
    spec:
      containers:
      - env:
        - name: HASURA_GRAPHQL_ENABLED_LOG_TYPES
          value: startup, http-log, webhook-log, websocket-log, query-log
        - name: HASURA_GRAPHQL_ENABLE_CONSOLE
          value: "true"
        - name: HASURA_GRAPHQL_ENABLE_TELEMETRY
          value: "false"
        - name: HASURA_GRAPHQL_STRINGIFY_NUMERIC_TYPES
          value: "true"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        image: inputoutput/cardano-graphql-hasura:8.0.0
        imagePullPolicy: IfNotPresent
        name: hasura
        ports:
        - containerPort: 8080
        resources: {}
        volumeMounts:
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: password
            path: POSTGRES_PASSWORD
          secretName: init0-postgresql-ha-postgresql
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: koios-api
    project_name: dandelion
  name: koios-api
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: koios-api
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: koios-api
        project_name: dandelion
    spec:
      containers:
      - command:
        - sh
        - -c
        - export PGRST_DB_URI=postgres://${PGUSER}:${PGPASSWORD}@${PGHOST}:${PGPORT}/${PGDATABASE};
          /bin/postgrest
        env:
        - name: PGHOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RO
              name: common-env
        - name: PGPORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: PGDATABASE
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGRST_DB_ANON_ROLE
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGRST_DB_SCHEMA
          value: grest
        - name: PGRST_MAX_ROWS
          value: "1000"
        image: gimbalabs/postgrest:v9.0.0
        imagePullPolicy: IfNotPresent
        name: koios-api
        ports:
        - containerPort: 3000
        resources: {}
        volumeMounts:
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
      initContainers:
      - command:
        - bash
        - -x
        - /configmap/initContainer-entrypoint
        env:
        - name: PGRST_DB_SCHEMA
          value: grest
        - name: PGHOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: PGPORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: PGDATABASE
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: PGUSER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        name: configure
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        volumeMounts:
        - mountPath: /configmap
          name: koios-configmap
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: passwords
            path: POSTGREST_RO_PASSWORD
          secretName: init0-postgresql-ha-pgpool-custom-users
      - configMap:
          name: koios-configmap
        name: koios-configmap
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: ogmios
    project_name: dandelion
  name: ogmios
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: ogmios
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: ogmios
        project_name: dandelion
    spec:
      containers:
      - command:
        - sh
        - -xc
        - ogmios --node-socket ${NODE_SOCKET_PATH} --host ${LISTEN_HOST} --port ${LISTEN_PORT}
          --log-level ${LOG_LEVEL} --node-config ${NODE_CONFIG_PATH}
        env:
        - name: OGMIOS_NETWORK
          value: testnet
        - name: NODE_CONFIG_PATH
          value: /config/network/cardano-node/config.json
        - name: LISTEN_HOST
          value: 0.0.0.0
        - name: LISTEN_PORT
          value: "1337"
        - name: NODE_SOCKET_PATH
          value: /node-ipc/node.socket
        - name: LOG_LEVEL
          value: Debug
        image: cardanosolutions/ogmios:v5.5.3-testnet
        imagePullPolicy: IfNotPresent
        name: ogmios
        ports:
        - containerPort: 1337
        resources: {}
        volumeMounts:
        - mountPath: /node-ipc
          name: node-ipc
        - mountPath: /opt/cardano/cnode/files
          name: ogmios-config
      - command:
        - sh
        - -c
        - rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        image: alpine/socat
        name: socat-socket-server
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      initContainers:
      - command:
        - bash
        - /configmap/initContainer-entrypoint
        env:
        - name: OGMIOS_NETWORK
          value: testnet
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        name: setup-node-config
        volumeMounts:
        - mountPath: /config
          name: ogmios-config
        - mountPath: /configmap
          name: ogmios-configmap
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - emptyDir: {}
        name: node-ipc
      - emptyDir: {}
        name: ogmios-config
      - configMap:
          name: ogmios-configmap
        name: ogmios-configmap
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: postgrest
    project_name: dandelion
  name: postgrest
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: postgrest
      project_name: dandelion
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: postgrest
        project_name: dandelion
    spec:
      containers:
      - command:
        - sh
        - -c
        - export PGRST_DB_URI=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB};
          /bin/postgrest
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RO
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGRST_DB_ANON_ROLE
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: PGRST_DB_SCHEMA
          value: public
        image: gimbalabs/postgrest:v9.0.0
        imagePullPolicy: IfNotPresent
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 3
        name: postgrest
        ports:
        - containerPort: 3000
        resources: {}
        volumeMounts:
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
      initContainers:
      - command:
        - bash
        - -x
        - /configmap/create-db-ro-user-entrypoint
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        name: configure
        volumeMounts:
        - mountPath: /configmap
          name: cardano-db-sync-configmap
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: passwords
            path: POSTGREST_RO_PASSWORD
          secretName: init0-postgresql-ha-pgpool-custom-users
      - configMap:
          name: postgrest-configmap
        name: postgrest-configmap
      - configMap:
          name: cardano-db-sync-configmap
        name: cardano-db-sync-configmap
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    note: free-apis!
  labels:
    app.kubernetes.io/component: postgresql
    app.kubernetes.io/instance: init0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: postgresql-ha
    app.kubernetes.io/version: 16.0.0
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    helm.sh/chart: postgresql-ha-11.9.8
    project_name: dandelion
    role: data
  name: init0-postgresql-ha-postgresql
  namespace: default
spec:
  podManagementPolicy: Parallel
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: postgresql
      app.kubernetes.io/instance: init0
      app.kubernetes.io/name: postgresql-ha
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      project_name: dandelion
      role: data
  serviceName: init0-postgresql-ha-postgresql-headless
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        app.kubernetes.io/component: postgresql
        app.kubernetes.io/instance: init0
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: postgresql-ha
        app.kubernetes.io/version: 16.0.0
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        helm.sh/chart: postgresql-ha-11.9.8
        project_name: dandelion
        role: data
    spec:
      affinity:
        nodeAffinity: null
        podAffinity: null
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchLabels:
                  app.kubernetes.io/component: postgresql
                  app.kubernetes.io/instance: init0
                  app.kubernetes.io/name: postgresql-ha
                  cardano_network: testnet
                  cardano_node_type: haskell
                  environment: production
                  project_name: dandelion
              topologyKey: kubernetes.io/hostname
            weight: 1
      containers:
      - env:
        - name: BITNAMI_DEBUG
          value: "false"
        - name: POSTGRESQL_VOLUME_DIR
          value: /bitnami/postgresql
        - name: PGDATA
          value: /bitnami/postgresql/data
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_DB
          value: cexplorer
        - name: POSTGRESQL_LOG_HOSTNAME
          value: "true"
        - name: POSTGRESQL_LOG_CONNECTIONS
          value: "false"
        - name: POSTGRESQL_LOG_DISCONNECTIONS
          value: "false"
        - name: POSTGRESQL_PGAUDIT_LOG_CATALOG
          value: "off"
        - name: POSTGRESQL_CLIENT_MIN_MESSAGES
          value: error
        - name: POSTGRESQL_SHARED_PRELOAD_LIBRARIES
          value: pgaudit, repmgr
        - name: POSTGRESQL_ENABLE_TLS
          value: "no"
        - name: POSTGRESQL_PORT_NUMBER
          value: "5432"
        - name: REPMGR_PORT_NUMBER
          value: "5432"
        - name: REPMGR_PRIMARY_PORT
          value: "5432"
        - name: MY_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: REPMGR_UPGRADE_EXTENSION
          value: "no"
        - name: REPMGR_PGHBA_TRUST_ALL
          value: "no"
        - name: REPMGR_MOUNTED_CONF_DIR
          value: /bitnami/repmgr/conf
        - name: REPMGR_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: REPMGR_PARTNER_NODES
          value: init0-postgresql-ha-postgresql-0.init0-postgresql-ha-postgresql-headless.$(REPMGR_NAMESPACE).svc.cluster.local,
        - name: REPMGR_PRIMARY_HOST
          value: init0-postgresql-ha-postgresql-0.init0-postgresql-ha-postgresql-headless.$(REPMGR_NAMESPACE).svc.cluster.local
        - name: REPMGR_NODE_NAME
          value: $(MY_POD_NAME)
        - name: REPMGR_NODE_NETWORK_NAME
          value: $(MY_POD_NAME).init0-postgresql-ha-postgresql-headless.$(REPMGR_NAMESPACE).svc.cluster.local
        - name: REPMGR_NODE_TYPE
          value: data
        - name: REPMGR_LOG_LEVEL
          value: NOTICE
        - name: REPMGR_CONNECT_TIMEOUT
          value: "5"
        - name: REPMGR_RECONNECT_ATTEMPTS
          value: "2"
        - name: REPMGR_RECONNECT_INTERVAL
          value: "3"
        - name: REPMGR_USERNAME
          value: repmgr
        - name: REPMGR_PASSWORD
          valueFrom:
            secretKeyRef:
              key: repmgr-password
              name: init0-postgresql-ha-postgresql
        - name: REPMGR_DATABASE
          value: repmgr
        - name: REPMGR_FENCE_OLD_PRIMARY
          value: "no"
        - name: REPMGR_CHILD_NODES_CHECK_INTERVAL
          value: "5"
        - name: REPMGR_CHILD_NODES_CONNECTED_MIN_COUNT
          value: "1"
        - name: REPMGR_CHILD_NODES_DISCONNECT_TIMEOUT
          value: "30"
        envFrom: null
        image: docker.io/bitnami/postgresql-repmgr:15.2.0-debian-11-r0
        imagePullPolicy: IfNotPresent
        lifecycle:
          preStop:
            exec:
              command:
              - /pre-stop.sh
              - "25"
        livenessProbe:
          exec:
            command:
            - bash
            - -ec
            - PGPASSWORD=$POSTGRES_PASSWORD psql -w -U "postgres" -d "cexplorer" -h
              127.0.0.1 -p 5432 -c "SELECT 1"
          failureThreshold: 6
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        name: postgresql
        ports:
        - containerPort: 5432
          name: postgresql
          protocol: TCP
        readinessProbe:
          exec:
            command:
            - bash
            - -ec
            - PGPASSWORD=$POSTGRES_PASSWORD psql -w -U "postgres" -d "cexplorer" -h
              127.0.0.1 -p 5432 -c "SELECT 1"
          failureThreshold: 6
          initialDelaySeconds: 5
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 5
        resources:
          limits: {}
          requests: {}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: false
          runAsGroup: 0
          runAsNonRoot: true
          runAsUser: 1001
          seccompProfile:
            type: RuntimeDefault
        volumeMounts:
        - mountPath: /bitnami/postgresql/conf/conf.d/
          name: postgresql-extended-config
        - mountPath: /bitnami/postgresql
          name: data
        - mountPath: /pre-stop.sh
          name: hooks-scripts
          subPath: pre-stop.sh
        - mountPath: /readiness-probe.sh
          name: hooks-scripts
          subPath: readiness-probe.sh
      hostIPC: false
      hostNetwork: false
      initContainers:
      - command:
        - sh
        - -c
        - mkdir -p /bitnami/postgresql/conf; chown -R 1001:1001 /bitnami/postgresql
        image: alpine
        imagePullPolicy: IfNotPresent
        name: conf-dir-workaround
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        volumeMounts:
        - mountPath: /bitnami/postgresql
          name: data
      securityContext:
        fsGroup: 1001
      volumes:
      - configMap:
          defaultMode: 493
          name: init0-postgresql-ha-postgresql-hooks-scripts
        name: hooks-scripts
      - configMap:
          name: init0-postgresql-ha-postgresql-extended-configuration
        name: postgresql-extended-config
  updateStrategy:
    type: RollingUpdate
  volumeClaimTemplates:
  - metadata:
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
      name: data
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 40Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-db-sync
    project_name: dandelion
  name: cardano-db-sync
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-db-sync
      project_name: dandelion
  serviceName: cardano-db-sync
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-db-sync
        project_name: dandelion
    spec:
      containers:
      - command:
        - sh
        - -x
        - /configmap/entrypoint
        env:
        - name: EXTENDED
          value: "true"
        - name: NETWORK
          value: testnet
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        image: gimbalabs/cardano-db-sync:13.0.5
        imagePullPolicy: IfNotPresent
        livenessProbe:
          exec:
            command:
            - bash
            - /configmap/liveness-healthcheck
          initialDelaySeconds: 600
          periodSeconds: 60
          timeoutSeconds: 120
        name: cardano-db-sync
        resources: {}
        volumeMounts:
        - mountPath: /node-ipc
          name: node-ipc
        - mountPath: /tmp
          name: node-ipc
        - mountPath: /run/secrets/common-env
          name: common-env
          readOnly: true
        - mountPath: /run/secrets/postgres-password
          name: postgres-password
          readOnly: true
        - mountPath: /var/lib/cexplorer
          name: dbsync-statedir
        - mountPath: /configmap
          name: cardano-db-sync-configmap
        - mountPath: /opt/cardano/cnode/files
          name: cardano-node-config
        workingDir: /var/lib/cexplorer
      - command:
        - sh
        - -c
        - rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        image: alpine/socat
        name: socat-socket-server
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      initContainers:
      - command:
        - bash
        - /configmap/initContainer-setup-node-config-entrypoint
        env:
        - name: NETWORK
          value: testnet
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: DB_SYNC_SNAPSHOT_TESTNET_S3_BUCKET_URI
          value: s3://updates-cardano-testnet/cardano-db-sync/13/
        - name: DB_SYNC_SNAPSHOT_MAINNET_S3_BUCKET_URI
          value: s3://update-cardano-mainnet.iohk.io/cardano-db-sync/12/
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        name: setup-node-config
        volumeMounts:
        - mountPath: /config
          name: cardano-node-config
        - mountPath: /configmap
          name: cardano-db-sync-configmap
      - command:
        - bash
        - -x
        - /configmap/initContainer-restore-snapshot-entrypoint
        env:
        - name: NETWORK
          value: mainnet
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: cardano-node-headless
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: DB_SYNC_SNAPSHOT_TESTNET_S3_BUCKET_URI
          value: s3://updates-cardano-testnet/cardano-db-sync/13.1/
        - name: DB_SYNC_SNAPSHOT_MAINNET_S3_BUCKET_URI
          value: s3://update-cardano-mainnet.iohk.io/cardano-db-sync/13.1/
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        imagePullPolicy: IfNotPresent
        name: restore-snapshot
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        volumeMounts:
        - mountPath: /configmap
          name: cardano-db-sync-configmap
        - mountPath: /db-sync-statedir
          name: dbsync-statedir
        - mountPath: /aux-data-dir
          name: node-ipc
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - emptyDir: {}
        name: node-ipc
      - emptyDir: {}
        name: cardano-node-config
      - configMap:
          name: common-env
        name: common-env
      - name: postgres-password
        secret:
          items:
          - key: password
            path: POSTGRES_PASSWORD
          secretName: init0-postgresql-ha-postgresql
      - name: dbsync-statedir
        persistentVolumeClaim:
          claimName: dbsync-statedir
      - configMap:
          name: cardano-db-sync-configmap
        name: cardano-db-sync-configmap
  updateStrategy:
    type: OnDelete
  volumeClaimTemplates:
  - metadata:
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
      name: dbsync-statedir
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 10Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    io.kompose.service: cardano-node
    project_name: dandelion
  name: cardano-node
spec:
  replicas: 1
  selector:
    matchLabels:
      cardano_network: testnet
      cardano_node_type: haskell
      environment: production
      io.kompose.service: cardano-node
      project_name: dandelion
  serviceName: cardano-node
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        io.kompose.service: cardano-node
        project_name: dandelion
    spec:
      containers:
      - command:
        - bash
        - -c
        - export HOME=/nonexistent; source $HOME/.baids/baids && set -x; ${NETWORK}-cnode-run-as-${CNODE_ROLE}
          +RTS -N2 --disable-delayed-os-memory-return -T -I0 -A16m
        env:
        - name: NETWORK
          value: iohk-tn
        - name: CNODE_HOST_ADDR
          value: 0.0.0.0
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: CNODE_DB_PATH
          value: /data/db
        - name: CNODE_TOPOLOGY_FILE
          value: /configmap/topology.json
        - name: CNODE_CONFIG_FILE
          value: /configmap/config.json
        image: repsistance/cardano-node:iohk-tn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        name: cardano-node
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /data/db
          name: node-db
        - mountPath: /ipc
          name: node-ipc
        - mountPath: /configmap
          name: cardano-node-configmap
      - command:
        - sh
        - -c
        - /app/chisel server -v --authfile /common-env/chisel-auth-file -p ${PORT:-40000}
        env:
        - name: PORT
          value: "40000"
        image: jpillora/chisel:1.7
        name: chisel-server
        ports:
        - containerPort: 40000
        volumeMounts:
        - mountPath: /common-env
          name: common-env
          readOnly: true
      - command:
        - sh
        - -c
        - socat TCP-LISTEN:${PORT},fork UNIX-CLIENT:/ipc/node.socket,ignoreeof
        env:
        - name: PORT
          value: "30000"
        image: alpine/socat
        name: socat-tcp-server
        ports:
        - containerPort: 30000
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      initContainers:
      - command:
        - sh
        - -c
        - rm -rf /data/db/lost+found
        image: busybox
        imagePullPolicy: IfNotPresent
        name: remove-lost-n-found
        resources: {}
        volumeMounts:
        - mountPath: /data/db
          name: node-db
      - command:
        - bash
        - -x
        - /configmap/initContainer-entrypoint
        env:
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: NETWORK
          value: testnet
        - name: CNODE_DB_PATH
          value: /data
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        name: restore-from-csnapshotio
        resources: {}
        volumeMounts:
        - mountPath: /configmap
          name: cardano-node-configmap
        - mountPath: /data/db
          name: node-db
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-db
        persistentVolumeClaim:
          claimName: node-db
      - emptyDir: {}
        name: node-ipc
      - configMap:
          name: common-env
        name: common-env
      - configMap:
          name: cardano-node
        name: cardano-node-configmap
  updateStrategy:
    type: OnDelete
  volumeClaimTemplates:
  - metadata:
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
      name: node-db
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 8Gi
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-active-stake-cache-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/active-stake-cache-update
            env:
            - name: KOIOS_REPOSITORY
              value: repsistance/koios-artifacts
            - name: KOIOS_BRANCH
              value: develop
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-active-stake-cache-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '*/15 * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-asset-info-cache-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/asset-info-cache-update
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-asset-info-cache-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '* * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-asset-registry-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/asset-registry-update
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-asset-registry-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '*/10 * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-epoch-info-cache-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/epoch-info-cache-update
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-epoch-info-cache-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '*/15 * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-pool-history-cache-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/pool-history-cache-update
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-pool-history-cache-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '*/10 * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-populate-next-epoch-nonce
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/populate-next-epoch-nonce
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-populate-next-epoch-nonce
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '*/10 * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-stake-distribution-new-accounts-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/stake-distribution-new-accounts-update
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-stake-distribution-new-accounts-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: 58 */6 * * *
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-stake-distribution-update
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/stake-distribution-update
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-stake-distribution-update
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 1740
  schedule: '*/30 * * * *'
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-stake-snapshot-cache
spec:
  jobTemplate:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      backoffLimit: 0
      template:
        metadata:
          annotations:
            note: free-apis!
          labels:
            cardano_network: testnet
            cardano_node_type: haskell
            environment: production
            project_name: dandelion
        spec:
          containers:
          - command:
            - bash
            - -x
            - /configmap/stake-snapshot-cache
            env:
            - name: KOIOS_REPOSITORY
              value: cardano-community/koios-artifacts
            - name: KOIOS_BRANCH
              value: main
            - name: CNODE_CONFIG_FILE
              value: /cardano-node-configmap/config.json
            - name: PGHOST
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_HOST_RW
                  name: common-env
            - name: PGPORT
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_PORT
                  name: common-env
            - name: PGDATABASE
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_DB
                  name: common-env
            - name: PGUSER
              valueFrom:
                configMapKeyRef:
                  key: POSTGRES_USER
                  name: common-env
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: init0-postgresql-ha-postgresql
            - name: POSTGRES_USER_RO
              valueFrom:
                secretKeyRef:
                  key: usernames
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: POSTGRES_PASSWORD_RO
              valueFrom:
                secretKeyRef:
                  key: passwords
                  name: init0-postgresql-ha-pgpool-custom-users
            - name: PGRST_DB_SCHEMA
              value: grest
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
            name: koios-stake-snapshot-cache
            volumeMounts:
            - mountPath: /configmap
              name: koios-configmap
            - mountPath: /cardano-node-configmap
              name: cardano-node-configmap
          restartPolicy: Never
          serviceAccountName: koios-deploy-sql
          volumes:
          - configMap:
              name: koios-configmap
            name: koios-configmap
          - configMap:
              name: cardano-node
            name: cardano-node-configmap
      ttlSecondsAfterFinished: 540
  schedule: '*/10 * * * *'
---
apiVersion: batch/v1
kind: Job
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: create-db-ro-user
spec:
  activeDeadlineSeconds: 600
  backoffLimit: 5
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      containers:
      - command:
        - bash
        - -x
        - /configmap/create-db-ro-user-entrypoint
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        name: create-db-ro-user
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        volumeMounts:
        - mountPath: /configmap
          name: cardano-db-sync-configmap
      restartPolicy: Never
      volumes:
      - configMap:
          name: cardano-db-sync-configmap
        name: cardano-db-sync-configmap
---
apiVersion: batch/v1
kind: Job
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: deploy-koios-sql
spec:
  backoffLimit: 5
  template:
    metadata:
      annotations:
        note: free-apis!
      labels:
        cardano_network: testnet
        cardano_node_type: haskell
        environment: production
        project_name: dandelion
    spec:
      containers:
      - command:
        - bash
        - -x
        - /configmap/koios-deploy-sql-entrypoint
        env:
        - name: KOIOS_REPOSITORY
          value: cardano-community/guild-operators
        - name: KOIOS_BRANCH
          value: alpha
        - name: KOIOS_VERSION
          value: 1.0.10
        - name: CNODE_HOME
          value: /opt/cardano/cnode
        - name: USESYSVARS
          value: "Y"
        - name: CNODE_CONFIG_FILE
          value: /cardano-node-configmap/config.json
        - name: PGHOST
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_HOST_RW
              name: common-env
        - name: PGPORT
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_PORT
              name: common-env
        - name: PGDATABASE
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_DB
              name: common-env
        - name: PGUSER
          valueFrom:
            configMapKeyRef:
              key: POSTGRES_USER
              name: common-env
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              key: password
              name: init0-postgresql-ha-postgresql
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              key: usernames
              name: init0-postgresql-ha-pgpool-custom-users
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              key: passwords
              name: init0-postgresql-ha-pgpool-custom-users
        image: repsistance/cardano-node:iohk-preprod-passive-8.1.1-0
        name: deploy-koios-sql
        volumeMounts:
        - mountPath: /configmap
          name: koios-configmap
        - mountPath: /cardano-node-configmap
          name: cardano-node-configmap
      restartPolicy: Never
      serviceAccountName: koios-deploy-sql
      volumes:
      - configMap:
          name: koios-configmap
        name: koios-configmap
      - configMap:
          name: cardano-node
        name: cardano-node-configmap
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-cardano-token-registry-api
spec:
  rules:
  - host: cardano-token-registry-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: cardano-token-registry
            port:
              number: 3042
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - cardano-token-registry-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-explorer-api
spec:
  rules:
  - host: explorer-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: cardano-explorer-api
            port:
              number: 8101
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - explorer-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-graphql-api
spec:
  rules:
  - host: graphql-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: cardano-graphql
            port:
              number: 3100
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - graphql-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: cardano-submit-api
spec:
  rules:
  - host: submit-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: cardano-submit-api
            port:
              number: 8091
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - submit-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: chisel-api
spec:
  rules:
  - host: chisel-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: chisel-server
            port:
              number: 40000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - chisel-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: koios-api
spec:
  rules:
  - host: koios-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: koios-api
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - koios-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: kupo-api
spec:
  rules:
  - host: kupo-api.iohk-testnet.local
    http:
      paths:
      - backend:
          service:
            name: kupo
            port:
              number: 1442
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - kupo-api.iohk-testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: ogmios-api
spec:
  rules:
  - host: ogmios-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: ogmios
            port:
              number: 1337
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - ogmios-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: postgrest-api
spec:
  rules:
  - host: postgrest-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: postgrest
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - postgrest-api.testnet.local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    note: free-apis!
  labels:
    cardano_network: testnet
    cardano_node_type: haskell
    environment: production
    project_name: dandelion
  name: rosetta-api
spec:
  rules:
  - host: rosetta-api.testnet.local
    http:
      paths:
      - backend:
          service:
            name: cardano-rosetta
            port:
              number: 8080
        path: /
        pathType: Prefix
  tls:
  - hosts:
    - rosetta-api.testnet.local
