#!/bin/bash

# Validation script for kustomize-cardano-k8s deployment
set -euo pipefail

echo "🔍 Validating kustomize-cardano-k8s deployment structure..."

# Check if kustomize is available
if ! command -v kustomize &> /dev/null; then
    echo "❌ kustomize command not found. Please install kustomize CLI."
    exit 1
fi

echo "✅ kustomize CLI found"

# Validate base structure
echo "🔍 Validating base structure..."

base_dirs=("common" "cardano-node" "cardano-db-sync" "postgres" "network-specific-patches/preprod")
for dir in "${base_dirs[@]}"; do
    if [ -d "base/$dir" ]; then
        echo "  ✅ base/$dir exists"
        if [ -f "base/$dir/kustomization.yaml" ]; then
            echo "  ✅ base/$dir/kustomization.yaml exists"
        else
            echo "  ❌ base/$dir/kustomization.yaml missing"
            exit 1
        fi
    else
        echo "  ❌ base/$dir missing"
        exit 1
    fi
done

# Validate overlays
echo "🔍 Validating overlay structure..."

overlay_dirs=("preprod-base" "preprod-full")
for dir in "${overlay_dirs[@]}"; do
    if [ -d "overlays/$dir" ]; then
        echo "  ✅ overlays/$dir exists"
        if [ -f "overlays/$dir/kustomization.yaml" ]; then
            echo "  ✅ overlays/$dir/kustomization.yaml exists"
        else
            echo "  ❌ overlays/$dir/kustomization.yaml missing"
            exit 1
        fi
    else
        echo "  ❌ overlays/$dir missing"
        exit 1
    fi
done

# Test kustomize builds
echo "🔍 Testing kustomize builds..."

overlays=("preprod-base" "preprod-full")
for overlay in "${overlays[@]}"; do
    echo "  Testing overlays/$overlay..."
    if kustomize build "overlays/$overlay/" > /dev/null; then
        echo "  ✅ overlays/$overlay builds successfully"
    else
        echo "  ❌ overlays/$overlay build failed"
        exit 1
    fi
done

# Validate generated manifests
echo "🔍 Validating generated manifests..."

output=$(kustomize build overlays/preprod-full/)

# Check for required resources
required_resources=(
    "ConfigMap"
    "Secret"
    "Service"
    "StatefulSet"
)

for resource in "${required_resources[@]}"; do
    if echo "$output" | grep -q "kind: $resource"; then
        echo "  ✅ $resource found in output"
    else
        echo "  ❌ $resource missing from output"
        exit 1
    fi
done

# Check for specific components
required_components=(
    "cardano-node"
    "cardano-db-sync"
    "postgres"
    "common-env"
)

for component in "${required_components[@]}"; do
    if echo "$output" | grep -q "name: $component"; then
        echo "  ✅ $component component found"
    else
        echo "  ❌ $component component missing"
        exit 1
    fi
done

# Check for namespace
if echo "$output" | grep -q "namespace: cardano-preprod"; then
    echo "  ✅ cardano-preprod namespace applied"
else
    echo "  ❌ cardano-preprod namespace not found"
    exit 1
fi

# Check for labels
if echo "$output" | grep -q "cardano_network: preprod"; then
    echo "  ✅ preprod network labels applied"
else
    echo "  ❌ preprod network labels missing"
    exit 1
fi

# Check for Mithril configuration
if echo "$output" | grep -q "MITHRIL_AGGREGATOR_ENDPOINT"; then
    echo "  ✅ Mithril configuration found"
else
    echo "  ❌ Mithril configuration missing"
    exit 1
fi

echo ""
echo "🎉 All validations passed!"
echo ""
echo "📋 Summary:"
echo "  - Base structure: ✅"
echo "  - Overlay structure: ✅"
echo "  - Kustomize builds: ✅"
echo "  - Required resources: ✅"
echo "  - Component presence: ✅"
echo "  - Namespace configuration: ✅"
echo "  - Network labels: ✅"
echo "  - Mithril integration: ✅"
echo ""
echo "🚀 Ready to deploy with:"
echo "   kubectl apply -k overlays/preprod-full/"
echo ""
echo "📊 Resource count in preprod-full:"
configmaps=$(echo "$output" | grep -c "kind: ConfigMap" || echo "0")
secrets=$(echo "$output" | grep -c "kind: Secret" || echo "0")
services=$(echo "$output" | grep -c "kind: Service" || echo "0")
statefulsets=$(echo "$output" | grep -c "kind: StatefulSet" || echo "0")

echo "   ConfigMaps: $configmaps"
echo "   Secrets: $secrets"
echo "   Services: $services"
echo "   StatefulSets: $statefulsets"
