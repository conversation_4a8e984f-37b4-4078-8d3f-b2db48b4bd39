{{- if .Values.dandelion.metrics_exporter.enabled -}}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dandelion-{{ .Values.dandelion.cardano.network }}-metrics-exporter
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: dandelion-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.argoProjectSuffix }}
  source:
    repoURL: {{ .Values.git.repositoryUrl }}
    targetRevision: {{ .Values.git.targetRevision }}
    path: base/metrics-exporter
    plugin:
      name: kustomized-kustomize-network
      env:
        - name: CARDANO_NETWORK
          value: {{ .Values.dandelion.cardano.network }}
        - name: MULTI_NODE_AFFINITY
          value: {{ .Values.dandelion.multiNode | quote }}

  destination:
    namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
    server: {{ .Values.spec.destination.server }}

  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true

{{- end }}
