apiVersion: batch/v1
kind: Job
metadata:
  name: create-ro-user
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: create-ro-user
        image: postgres:15
        command:
        - /bin/bash
        - -c
        - |
          set -euo pipefail
          
          echo "Creating read-only user for carnado..."
          
          # Wait for database to be ready
          until PGPASSWORD=${POSTGRES_PASSWORD} psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c '\q'; do
            echo "Waiting for database..."
            sleep 5
          done
          
          # Create read-only user
          PGPASSWORD=${POSTGRES_PASSWORD} psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -d ${POSTGRES_DB} <<EOF
          DO \$\$
          BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '${POSTGRES_USER_RO}') THEN
              CREATE ROLE ${POSTGRES_USER_RO} WITH LOGIN PASSWORD '${POSTGRES_PASSWORD_RO}';
            END IF;
          END
          \$\$;
          
          GRANT CONNECT ON DATABASE ${POSTGRES_DB} TO ${POSTGRES_USER_RO};
          GRANT USAGE ON SCHEMA public TO ${POSTGRES_USER_RO};
          GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${POSTGRES_USER_RO};
          ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO ${POSTGRES_USER_RO};
          EOF
          
          echo "Read-only user created successfully"
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: carnado-postgresql-ha-postgresql
              key: password
        - name: POSTGRES_USER_RO
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER_RO
        - name: POSTGRES_PASSWORD_RO
          value: "readonly_password_change_me"
