apiVersion: v1
kind: Service
metadata:
  name: cardano-node
  labels:
    io.kompose.service: cardano-node
spec:
  selector:
    io.kompose.service: cardano-node
  ports:
  - port: 3001
    targetPort: 3001
    name: cardano-node
  - port: 12781
    targetPort: 12781
    name: ekg
  - port: 13788
    targetPort: 13788
    name: prometheus
  - port: 30000
    targetPort: 30000
    name: socket-relay
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: cardano-node-headless
  labels:
    io.kompose.service: cardano-node
spec:
  selector:
    io.kompose.service: cardano-node
  ports:
  - port: 3001
    targetPort: 3001
    name: cardano-node
  - port: 12781
    targetPort: 12781
    name: ekg
  - port: 13788
    targetPort: 13788
    name: prometheus
  - port: 30000
    targetPort: 30000
    name: socket-relay
  clusterIP: None  # Headless service for StatefulSet
