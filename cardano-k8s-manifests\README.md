# Cardano Kubernetes Manifests - Preprod Testnet

This directory contains complete Kubernetes manifests for deploying a Cardano infrastructure stack on the preprod testnet, translated from the Docker Compose configuration with advanced features from kustomize-dandelion.

## Architecture Overview

The deployment includes:

- **Cardano Node**: Blockchain node with Mithril fast sync for preprod testnet
- **Cardano DB Sync**: Database synchronization service for blockchain data
- **PostgreSQL**: High-performance database backend with optimized settings
- **Networking**: Proper inter-component communication with socket relays

## Features

### ✅ **Production-Ready Components**
- StatefulSets with persistent storage for all stateful components
- Proper resource limits and health checks
- Init containers for dependency management and setup
- Optimized PostgreSQL configuration from docker-compose

### ✅ **Mithril Fast Sync Integration**
- Automated Mithril client download and setup
- Fast blockchain state restoration for preprod network
- Configurable snapshot restoration in init containers

### ✅ **Preprod Network Configuration**
- Correct network magic and genesis file configurations
- Optimized topology for preprod network peers
- P2P networking enabled with proper peer targets

### ✅ **Advanced Networking**
- Socket relay containers for cardano-node ↔ cardano-db-sync communication
- Headless services for StatefulSet networking
- Proper port exposure for monitoring and metrics

## Quick Deployment

### Prerequisites
- Kubernetes cluster with at least 16GB RAM and 500GB storage
- kubectl configured to access your cluster
- Default storage class available for PersistentVolumeClaims

### Deploy Everything
```bash
# Deploy all components at once
kubectl apply -f deploy.yaml

# Or deploy individual components
kubectl apply -f namespace.yaml
kubectl apply -f configmap-common.yaml
kubectl apply -f secret-postgres.yaml
kubectl apply -f statefulset-postgres.yaml
kubectl apply -f configmap-cardano-node.yaml
kubectl apply -f statefulset-cardano-node.yaml
kubectl apply -f service-cardano-node.yaml
kubectl apply -f configmap-cardano-db-sync.yaml
kubectl apply -f statefulset-cardano-db-sync.yaml
kubectl apply -f service-cardano-db-sync.yaml
```

### Monitor Deployment
```bash
# Check pod status
kubectl get pods -n cardano-preprod

# Watch logs
kubectl logs -f statefulset/cardano-node -n cardano-preprod
kubectl logs -f statefulset/cardano-db-sync -n cardano-preprod -c cardano-db-sync
kubectl logs -f statefulset/postgres -n cardano-preprod

# Check storage
kubectl get pvc -n cardano-preprod
```

## Component Details

### PostgreSQL Database
- **Image**: `postgres:17.2-alpine`
- **Storage**: 200GB persistent volume
- **Optimizations**: 1GB maintenance_work_mem, 4 parallel maintenance workers
- **Health Checks**: pg_isready probes with proper timeouts

### Cardano Node
- **Image**: `ghcr.io/intersectmbo/cardano-node:10.1.4`
- **Network**: Preprod testnet with P2P enabled
- **Storage**: 100GB for blockchain data
- **Fast Sync**: Mithril client integration for rapid bootstrap
- **Monitoring**: EKG (port 12781) and Prometheus (port 13788) endpoints

### Cardano DB Sync
- **Image**: `ghcr.io/intersectmbo/cardano-db-sync:13.6.0.5`
- **Storage**: 150GB for sync state data
- **Dependencies**: Waits for both PostgreSQL and Cardano Node
- **Monitoring**: Prometheus metrics on port 8080

## Resource Requirements

### Minimum Requirements
- **Total CPU**: 6 cores
- **Total RAM**: 16GB
- **Total Storage**: 500GB

### Recommended for Production
- **Total CPU**: 12 cores
- **Total RAM**: 32GB
- **Total Storage**: 1TB

### Per Component
| Component | CPU Request | CPU Limit | Memory Request | Memory Limit | Storage |
|-----------|-------------|-----------|----------------|--------------|---------|
| PostgreSQL | 1000m | 2000m | 2Gi | 4Gi | 200Gi |
| Cardano Node | 2000m | 4000m | 4Gi | 8Gi | 100Gi |
| Cardano DB Sync | 2000m | 4000m | 4Gi | 8Gi | 150Gi |

## Network Configuration

### Preprod Testnet Settings
- **Network Magic**: RequiresMagic
- **Genesis Files**: Downloaded from official preprod endpoints
- **Topology**: Connects to `preprod-node.world.dev.cardano.org:30000`
- **P2P**: Enabled with 20 active peers, 50 established peers

### Service Networking
- **cardano-node**: Ports 3001 (node), 12781 (EKG), 13788 (Prometheus), 30000 (socket relay)
- **postgres**: Port 5432
- **cardano-db-sync**: Port 8080 (Prometheus)

## Monitoring and Observability

### Health Checks
- **PostgreSQL**: `pg_isready` checks every 10 seconds
- **Cardano Node**: EKG endpoint health check every 60 seconds
- **Cardano DB Sync**: Database connectivity check every 60 seconds

### Metrics Endpoints
- **Cardano Node EKG**: `http://cardano-node:12781`
- **Cardano Node Prometheus**: `http://cardano-node:13788/metrics`
- **Cardano DB Sync Prometheus**: `http://cardano-db-sync:8080/metrics`

## Troubleshooting

### Common Issues

1. **Pods Stuck in Pending**
   - Check storage class availability: `kubectl get storageclass`
   - Verify resource availability: `kubectl describe nodes`

2. **Mithril Download Failures**
   - Check internet connectivity from pods
   - Verify Mithril aggregator endpoint accessibility

3. **Database Connection Issues**
   - Ensure PostgreSQL is ready: `kubectl logs postgres-0 -n cardano-preprod`
   - Check secret configuration: `kubectl get secret postgres-secret -n cardano-preprod -o yaml`

4. **Cardano Node Sync Issues**
   - Monitor logs for peer connections: `kubectl logs cardano-node-0 -n cardano-preprod`
   - Check topology configuration and network connectivity

### Useful Commands
```bash
# Access PostgreSQL
kubectl exec -it postgres-0 -n cardano-preprod -- psql -U postgres -d cexplorer

# Check cardano-node socket
kubectl exec -it cardano-node-0 -n cardano-preprod -- ls -la /ipc/

# Monitor resource usage
kubectl top pods -n cardano-preprod

# Scale down for maintenance
kubectl scale statefulset cardano-db-sync --replicas=0 -n cardano-preprod
kubectl scale statefulset cardano-node --replicas=0 -n cardano-preprod
```

## Security Considerations

- Change the default PostgreSQL password in `secret-postgres.yaml`
- Consider using external secrets management (e.g., Vault, AWS Secrets Manager)
- Implement network policies for pod-to-pod communication restrictions
- Regular security updates for container images

## Cleanup

```bash
# Remove all resources
kubectl delete namespace cardano-preprod

# Or remove individual components
kubectl delete -f deploy.yaml
```

## Migration from Docker Compose

This Kubernetes deployment maintains compatibility with the Docker Compose configuration:

- Same container images and versions
- Equivalent environment variables and configuration
- Preserved volume mount patterns (`node-ipc:/ipc`)
- Identical PostgreSQL optimization settings
- Same health check logic

The main enhancements over Docker Compose:
- Production-ready resource management
- Advanced networking with Kubernetes services
- Mithril fast sync integration
- Comprehensive monitoring and observability
- Scalable and maintainable infrastructure patterns
