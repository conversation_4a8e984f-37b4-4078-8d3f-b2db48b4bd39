{{- if .Values.dandelion.cardano_db_sync.enabled -}}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dandelion-{{ .Values.dandelion.cardano.network }}-postgres-ha
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: dandelion-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.argoProjectSuffix }}
  source:
    repoURL: https://charts.bitnami.com/bitnami
    chart: postgresql-ha
    targetRevision: 6.6.6
    helm:
      releaseName: init0
      parameters:
        # This could be used to test dev branches.
        - name: "git.targetRevision"
          value: {{ .Values.git.targetRevision | default "HEAD" }}
        - name: "dandelion.cardano.network"
          value: {{ .Values.dandelion.cardano.network | default "testnet" }}
      values: |
        postgresql:
          replicaCount: 1
          updateStrategyType: OnDelete
          password: {{ .Values.dandelion.db.adminPassword | default "cdbsCHANGEME" }}
          database: cexplorer
          usePasswordFile: false
          repmgrUsername: repmgruser
          repmgrPassword: {{ .Values.dandelion.db.repmgrPassword | default "repmgrpassword" }}
          repmgrDatabase: repmgrdatabase
          {{- if .Values.dandelion.multiNode }}
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: k8s.dandelion.link/role
                    operator: In
                    values:
                    - db-main
                  - key: k8s.dandelion.link/cardano-network
                    operator: In
                    values:
                    - mainnet
          {{- end }}
        pgpool:
          customUsers:
            usernames: 'dandelion_ro'
            passwords: {{ .Values.dandelion.db.roPassword | default "CHANGEME" }}
        metrics:
          enabled: true
        persistence:
          size: {{ .Values.dandelion.db.diskSize | default "10Gi" }}

  destination:
    namespace: dl-{{ .Values.dandelion.cardano.network }}{{ .Values.dandelion.namespaceSuffix }}
    server: {{ .Values.spec.destination.server }}
{{- end }}
