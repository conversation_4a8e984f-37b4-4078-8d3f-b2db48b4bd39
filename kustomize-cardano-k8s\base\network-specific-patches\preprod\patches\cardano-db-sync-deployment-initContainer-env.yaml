- op: replace
  path: /spec/template/spec/initContainers/1/env
  value:
    - name: NETWORK
      value: preprod
    - name: CARDANO_NODE_SOCKET_TCP_HOST
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: CA<PERSON><PERSON><PERSON>_NODE_SOCKET_TCP_HOST
    - name: <PERSON><PERSON><PERSON><PERSON>_NODE_SOCKET_TCP_PORT
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: CARD<PERSON>O_NODE_SOCKET_TCP_PORT
    - name: SOCAT_TIMEOUT
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: SOCAT_TIMEOUT
    - name: CA<PERSON><PERSON><PERSON>_NODE_SOCKET_PATH
      value: /ipc/node.socket
    - name: RESTORE_SNAPSHOT
      value: "false"
    - name: POSTGRES_HOST
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_HOST_RW
    - name: POSTGRES_PORT
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_PORT
    - name: P<PERSON><PERSON>GRES_DB
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_DB
    - name: POSTGRES_USER
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_USER
    - name: PGPASSWORD
      valueFrom:
        secretKeyRef:
          name: postgres-secret
          key: password
