# Copyright 2020 HAProxy Technologies LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

## Default values for HAProxy

## Configure Service Account
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
serviceAccount:
  create: true
  name:

## Default values for image
image:
  repository: haproxytech/haproxy-alpine    # can be changed to use CE or EE images
  tag: "{{ .Chart.AppVersion }}"
  pullPolicy: IfNotPresent

## Deployment or DaemonSet pod mode
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/daemonset/
kind: DaemonSet    # can be 'Deployment' or 'DaemonSet'
replicaCount: 2   # used only for Deployment mode

# livenessProbe to deployment and daemonset
livenessProbe:
  enabled: false
  failureThreshold: 3
  successThreshold: 1
  initialDelaySeconds: 15
  timeoutSeconds: 1
  tcpSocket:
    port: 1024   # modify to your config
  periodSeconds: 20

## DaemonSet configuration
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/daemonset/
daemonset:
  useHostNetwork: false   # also modify dnsPolicy accordingly
  useHostPort: false
  hostPorts:
    http: 80
    https: 443
    stat: 1024

## Init Containers
## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
initContainers: []
# - name: sysctl
#   image: "busybox:musl"
#   command:
#     - /bin/sh
#     - -c
#     - sysctl -w net.core.somaxconn=65536
#   securityContext:
#     privileged: true

## Pod termination grace period
## ref: https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/
terminationGracePeriodSeconds: 60

## Private Registry configuration
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imageCredentials:
  registry: null    ## EE images require setting this
  username: null    ## EE images require setting this
  password: null    ## EE images require setting this
existingImagePullSecret: null

## Container listener port configuration
## ref: https://kubernetes.io/docs/concepts/services-networking/connect-applications-service/
containerPorts:   # has to match hostPorts when useHostNetwork is true
  http: 80
  https: 443
  stat: 1024

## Deployment strategy definition
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy
strategy: {}
#  rollingUpdate:
#    maxSurge: 25%
#    maxUnavailable: 25%
#  type: RollingUpdate

## Pod PriorityClass
## ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass
priorityClassName: ""

## Container lifecycle handlers
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/attach-handler-lifecycle-event/
lifecycle: {}
  ## Example preStop for graceful shutdown
  # preStop:
  #   exec:
  #     command: ["/bin/sh", "-c", "kill -USR1 $(pidof haproxy); while killall -0 haproxy; do sleep 1; done"]

## Additional volumeMounts to the controller main container
#extraVolumeMounts: []
## Example empty volume mounts when using securityContext->readOnlyRootFilesystem
# - name: etc-haproxy
#   mountPath: /etc/haproxy
# - name: tmp
#   mountPath: /tmp
# - name: var-state-haproxy
#   mountPath: /var/state/haproxy

## Additional volumes to the controller pod
#extraVolumes: []
## Example empty volumes when using securityContext->readOnlyRootFilesystem
# - name: etc-haproxy
#   emptyDir: {}
# - name: tmp
#   emptyDir: {}
# - name: var-state-haproxy
#   emptyDir: {}

#extraVolumeMounts:
#  - name: extra-conf
#    mountPath: /tmp/chart/fix
#  - mountPath: /configmap
#    name: dandelion-haproxy-configmap

extraVolumes:
  - name: extra-conf
    emptyDir: {}

## HAProxy daemon configuration
# ref: https://www.haproxy.org/download/2.2/doc/configuration.txt
# DISABLED logging:
    #log stdout format raw local0
config: |
  global
    log stdout format raw local0
    nbthread 4
    maxconn 1000
    insecure-fork-wanted
    external-check
    spread-checks 5

  defaults
    log global
    option httplog
    option dontlognull
    timeout client 20s
    timeout connect 20s
    timeout server 20s
    option abortonclose
    retries 2

  backend no-match
    mode http
    http-request deny deny_status 400

  resolvers resolvconf
    parse-resolv-conf
    # How long to "hold" a backend server's up/down status depending on the name resolution status.
    # For example, if an NXDOMAIN response is returned, keep the backend server in its current state (up) for
    # at least another 30 seconds before marking it as down due to DNS not having a record for it.
    hold valid    10s
    hold other    30s
    hold refused  30s
    hold nx       30s
    hold timeout  30s
    hold obsolete 30s
    # How many times to retry a query
    resolve_retries 3
    # How long to wait between retries when no valid response has been received
    timeout retry 1s
    # How long to wait for a successful resolution
    timeout resolve 1s

## Additional secrets to mount as volumes
## This is expected to be an array of dictionaries specifying the volume name, secret name and mount path
#mountedSecrets: []
mountedSecrets:
  - volumeName: cert-pem
    secretName: self-signed-ssl
    mountPath: /usr/local/etc/ssl

## Pod Node assignment
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
nodeSelector: {}

## Node Taints and Tolerations for pod-node cheduling through attraction/repelling
## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
tolerations: []
#  - key: "key"
#    operator: "Equal|Exists"
#    value: "value"
#    effect: "NoSchedule|PreferNoSchedule|NoExecute(1.6 only)"

## Node Affinity for pod-node scheduling constraints
## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
affinity: {}

## Pod DNS Config
## ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/
dnsConfig: {}

## Pod DNS Policy
## Change this to ClusterFirstWithHostNet in case you have useHostNetwork set to true
## ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-s-dns-policy
dnsPolicy: ClusterFirst

## Additional labels to add to the pod container metadata
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}
#  key: value

## Additional annotations to add to the pod container metadata
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}
#  key: value

## Disableable use of Pod Security Policy
## ref: https://kubernetes.io/docs/concepts/policy/pod-security-policy/
podSecurityPolicy:
  create: true

## Pod Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
podSecurityContext: {}

## Container Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/
securityContext:
  enabled: false
  runAsUser: 1000
  runAsGroup: 1000

## Compute Resources
## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
resources:
#  limits:
#    cpu: 100m
#    memory: 64Mi
  requests:
    cpu: 100m
    memory: 64Mi

## Horizontal Pod Scaler
## Only to be used with Deployment kind
## ref: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 7
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

## Pod Disruption Budget
## Only to be used with Deployment kind
## ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
PodDisruptionBudget:
  enable: false
  # maxUnavailable: 1
  # minAvailable: 1

## Service configuration
## ref: https://kubernetes.io/docs/concepts/services-networking/service/
service:
  type: LoadBalancer   # can be 'LoadBalancer'

  ## Service ClusterIP
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
  clusterIP: ""

  ## LoadBalancer IP
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#loadbalancer
  loadBalancerIP: ""

  ## Source IP ranges permitted to access Network Load Balancer
  # ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/
  loadBalancerSourceRanges: []

  ## Service annotations
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  annotations: {}

  ## Service externalTrafficPolicy
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#external-traffic-policy
  # externalTrafficPolicy: Cluster
