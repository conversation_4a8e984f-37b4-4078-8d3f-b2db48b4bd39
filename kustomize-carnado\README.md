# Kustomize Carnado - Cardano Infrastructure Deployment

This repository contains Kustomize configurations for deploying Cardano infrastructure components on Kubernetes, specifically designed for the Cardano preprod testnet with Mithril fast sync capabilities.

## Overview

The `kustomize-carnado` deployment provides:
- **cardano-node**: Cardano blockchain node with Mithril fast sync support
- **cardano-db-sync**: Database synchronization service for blockchain data
- **PostgreSQL**: High-availability database backend
- **Network Configuration**: Optimized for Cardano preprod testnet

## Architecture

```
kustomize-carnado/
├── base/                           # Base Kubernetes manifests
│   ├── common/                     # Common configurations (env, rbac)
│   ├── cardano-node/              # Cardano node base configuration
│   ├── cardano-db-sync/           # DB sync base configuration
│   └── network-specific-patches/   # Network-specific patches
│       └── testnet-preprod/       # Preprod network patches
└── overlays/                      # Environment-specific overlays
    └── testnet-preprod/           # Preprod testnet deployment
```

## Key Features

### Mithril Fast Sync
- Cardano node configured with Mithril client for fast blockchain state restoration
- Eliminates need to sync from genesis block
- Significantly reduces initial sync time

### Preprod Network Configuration
- Configured for Cardano preprod testnet
- Proper network magic and genesis file configurations
- Optimized topology for preprod network peers

### High Availability
- StatefulSet deployments for persistent storage
- PostgreSQL with persistent volumes
- Proper resource limits and health checks

## Deployment

### Prerequisites
- Kubernetes cluster with sufficient resources
- kubectl configured to access the cluster
- kustomize CLI tool

### Deploy to Preprod Testnet

```bash
# Deploy the complete stack
kubectl apply -k kustomize-carnado/overlays/testnet-preprod/

# Check deployment status
kubectl get pods -l project_name=carnado

# Monitor logs
kubectl logs -f statefulset/carnado-cardano-node
kubectl logs -f statefulset/carnado-cardano-db-sync
```

### Resource Requirements

#### Minimum Requirements:
- **cardano-node**: 4 CPU, 8GB RAM, 50GB storage
- **cardano-db-sync**: 2 CPU, 4GB RAM, 100GB storage
- **PostgreSQL**: 1 CPU, 2GB RAM, 200GB storage

#### Recommended for Production:
- **cardano-node**: 8 CPU, 16GB RAM, 100GB storage
- **cardano-db-sync**: 4 CPU, 8GB RAM, 500GB storage
- **PostgreSQL**: 4 CPU, 8GB RAM, 1TB storage

## Configuration

### Environment Variables
Key environment variables are configured in `base/common/common-env.yaml`:
- `POSTGRES_HOST`: PostgreSQL service hostname
- `POSTGRES_DB`: Database name (cexplorer)
- `POSTGRES_USER`: Database user
- Network-specific configurations in patches

### Network Configuration
Network-specific settings are applied via Kustomize components in:
`base/network-specific-patches/testnet-preprod/`

### Mithril Configuration
Mithril client is configured in the cardano-node init container:
- Aggregator endpoint: `https://aggregator.testing-preprod.api.mithril.network/aggregator`
- Genesis verification key for preprod network
- Automatic snapshot download and restoration

## Monitoring

### Health Checks
- Cardano node: Prometheus metrics on port 12798
- DB sync: Liveness probe checking database connectivity
- PostgreSQL: Standard PostgreSQL health checks

### Logs
```bash
# View cardano-node logs
kubectl logs -f statefulset/carnado-cardano-node -c cardano-node

# View db-sync logs
kubectl logs -f statefulset/carnado-cardano-db-sync -c cardano-db-sync

# View PostgreSQL logs
kubectl logs -f statefulset/carnado-postgresql-ha-postgresql
```

## Troubleshooting

### Common Issues

1. **Slow Initial Sync**: Ensure Mithril is properly configured and network connectivity is good
2. **Database Connection Issues**: Check PostgreSQL service and credentials
3. **Storage Issues**: Verify PVC provisioning and storage class availability

### Useful Commands

```bash
# Check PVC status
kubectl get pvc -l project_name=carnado

# Describe problematic pods
kubectl describe pod <pod-name>

# Access cardano-node shell
kubectl exec -it statefulset/carnado-cardano-node -c cardano-node -- bash

# Check cardano-node socket
kubectl exec -it statefulset/carnado-cardano-node -c cardano-node -- ls -la /ipc/
```

## Security Considerations

- Change default passwords in secrets
- Use proper RBAC configurations
- Consider network policies for pod-to-pod communication
- Regular security updates for container images

## Contributing

When making changes:
1. Test changes in a development environment
2. Follow existing naming conventions
3. Update documentation as needed
4. Ensure backward compatibility where possible
