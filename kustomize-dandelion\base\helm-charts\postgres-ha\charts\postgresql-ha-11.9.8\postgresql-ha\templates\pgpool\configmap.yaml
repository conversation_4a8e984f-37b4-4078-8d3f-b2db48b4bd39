{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.pgpool.configuration (not .Values.pgpool.configurationCM) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-configuration" (include "postgresql-ha.pgpool" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" ( include "common.images.version" ( dict "imageRoot" .Values.pgpool.image "chart" .Chart ) ) }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonLabels $versionLabel ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: pgpool
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  pgpool.conf: |-
    {{- include "common.tplvalues.render" (dict "value" .Values.pgpool.configuration "context" $) | nindent 4 }}
{{- end }}
