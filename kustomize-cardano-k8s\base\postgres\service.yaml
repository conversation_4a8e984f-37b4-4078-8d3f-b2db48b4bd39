apiVersion: v1
kind: Service
metadata:
  name: postgres
  labels:
    app: postgres
    component: database
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  clusterIP: None  # Headless service for StatefulSet
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-external
  labels:
    app: postgres
    component: database
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  type: ClusterIP
