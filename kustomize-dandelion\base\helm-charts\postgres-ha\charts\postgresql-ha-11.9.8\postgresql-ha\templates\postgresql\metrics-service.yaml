{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-metrics" (include "postgresql-ha.postgresql" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: postgresql
    {{- if .Values.metrics.serviceMonitor.selector }}
    {{- include "common.tplvalues.render" (dict "value" .Values.metrics.serviceMonitor.selector "context" $) | nindent 4 }}
    {{- end }}
  {{- if or .Values.metrics.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.metrics.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.metrics.service.type }}
  {{- if (or (eq .Values.metrics.service.type "LoadBalancer") (eq .Values.metrics.service.type "NodePort")) }}
  externalTrafficPolicy: {{ .Values.metrics.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{ if .Values.metrics.service.clusterIP }}
  clusterIP: {{ .Values.metrics.service.clusterIP }}
  {{ end }}
  {{ if eq .Values.metrics.service.type "LoadBalancer" }}
  loadBalancerSourceRanges: {{ .Values.metrics.service.loadBalancerSourceRanges }}
  {{ end }}
  {{- if (and (eq .Values.metrics.service.type "LoadBalancer") (not (empty .Values.metrics.service.loadBalancerIP))) }}
  loadBalancerIP: {{ .Values.metrics.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: metrics
      port: {{ .Values.metrics.service.ports.metrics }}
      targetPort: metrics
      {{- if (and (or (eq .Values.metrics.service.type "NodePort") (eq .Values.metrics.service.type "LoadBalancer")) (not (empty .Values.metrics.service.nodePorts.metrics))) }}
      nodePort: {{ .Values.metrics.service.nodePorts.metrics }}
      {{- else if eq .Values.metrics.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.postgresql.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: postgresql
{{- end }}
