apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: cardano-explorer-api
  name: cardano-explorer-api
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: cardano-explorer-api
  template:
    metadata:
      labels:
        io.kompose.service: cardano-explorer-api
    spec:
      containers:
      - env:
        - name: NETWORK
          value: mainnet
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-postgresql
              key: password
        image: inputoutput/cardano-explorer-api:3.1.2
        imagePullPolicy: IfNotPresent
        name: cardano-explorer-api
        ports:
        - containerPort: 8100
        # FIXME: postgres exploits with this :shrug:
        #WARNING:  there is already a transaction in progress
        #WARNING:  there is no transaction in progress
        #livenessProbe:
        #  failureThreshold: 3
        #  httpGet:
        #    httpHeaders:
        #    - name: Custom-Header
        #      value: Awesome
        #    path: /api/genesis/summary
        #    port: 8100
        #    scheme: HTTP
        #  periodSeconds: 10
        #  successThreshold: 1
        #  timeoutSeconds: 1
        #readinessProbe:
        #  failureThreshold: 3
        #  httpGet:
        #    httpHeaders:
        #    - name: Custom-Header
        #      value: Awesome
        #    path: /api/genesis/summary
        #    port: 8100
        #    scheme: HTTP
        #  initialDelaySeconds: 5
        #  periodSeconds: 5
        #  successThreshold: 1
        #  timeoutSeconds: 1
        resources: {}
        volumeMounts:
        - name: common-env
          mountPath: /run/secrets/common-env
          readOnly: true
        - name: postgres-password
          mountPath: /run/secrets/postgres-password
          readOnly: true
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: common-env
        configMap:
          name: common-env
      - name: postgres-password
        secret:
          secretName: init0-postgresql-ha-postgresql
          items:
          - key: password
            path: POSTGRES_PASSWORD
