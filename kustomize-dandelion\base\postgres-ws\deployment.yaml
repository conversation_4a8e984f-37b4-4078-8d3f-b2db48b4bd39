apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: postgres-ws
  name: postgres-ws
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: postgres-ws
  template:
    metadata:
      labels:
        io.kompose.service: postgres-ws
    spec:
      containers:
      - env:
        - name: PGWS_JWT_SECRET
          value: "auwhfdnskjhewfi34uwehdlaehsfkuaeiskjnfduierhfsiweskjcnzeiluwhskdewishdnpwe"
        - name: PGWS_ROOT_PATH
          value: /home/<USER>/client-example
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RO
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASS
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        image: diogob/postgres-websockets
        command: ['sh', '-c', 'PGWS_DB_URI="postgres://${POSTGRES_USER}:${POSTGRES_PASS}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}" postgres-websockets']
        imagePullPolicy: IfNotPresent
        name: postgres-ws
        ports:
        - containerPort: 3000
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 15
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 15
        resources: {}
        volumeMounts:
        - name: common-env
          mountPath: /run/secrets/common-env
          readOnly: true
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: common-env
        configMap:
          name: common-env
