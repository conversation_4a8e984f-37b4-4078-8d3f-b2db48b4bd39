# Kustomize Cardano K8s - Modular Cardano Infrastructure

This repository contains a modular Kustomize-based deployment for Cardano infrastructure components, following the organizational patterns established in kustomize-dandelion. The structure provides a clean separation between base components and environment-specific configurations.

## Architecture Overview

The deployment follows the Kustomize best practices with a clear separation of concerns:

- **Base Components**: Reusable, environment-agnostic Kubernetes manifests
- **Network Patches**: Network-specific configurations applied as components
- **Overlays**: Environment-specific deployments that compose base components

## Directory Structure

```
kustomize-cardano-k8s/
├── base/                                    # Base Kubernetes manifests
│   ├── common/                             # Common configurations
│   │   ├── common-env.yaml                 # Environment variables
│   │   ├── postgres-secret.yaml           # Database credentials
│   │   └── kustomization.yaml
│   ├── cardano-node/                       # Cardano blockchain node
│   │   ├── configmap.yaml                 # Node configuration and scripts
│   │   ├── statefulset.yaml               # Node StatefulSet with Mithril
│   │   ├── service.yaml                   # Node services
│   │   └── kustomization.yaml
│   ├── cardano-db-sync/                    # Database synchronization
│   │   ├── configmap.yaml                 # DB sync configuration
│   │   ├── statefulset.yaml               # DB sync StatefulSet
│   │   ├── service.yaml                   # DB sync service
│   │   └── kustomization.yaml
│   ├── postgres/                           # PostgreSQL database
│   │   ├── statefulset.yaml               # PostgreSQL StatefulSet
│   │   ├── service.yaml                   # PostgreSQL services
│   │   └── kustomization.yaml
│   └── network-specific-patches/           # Network-specific patches
│       └── preprod/                        # Preprod network patches
│           ├── kustomization.yaml          # Component definition
│           └── patches/                    # JSON patches for preprod
│               ├── cardano-node-*.yaml
│               └── cardano-db-sync-*.yaml
└── overlays/                               # Environment-specific deployments
    ├── preprod-base/                       # Base preprod (node only)
    │   └── kustomization.yaml
    └── preprod-full/                       # Full preprod stack
        └── kustomization.yaml
```

## Components

### Base Components

#### Common (`base/common/`)
- **common-env.yaml**: Shared environment variables and configuration
- **postgres-secret.yaml**: Database credentials (change in production!)

#### Cardano Node (`base/cardano-node/`)
- **Mithril Integration**: Fast sync using Mithril client for preprod network
- **P2P Networking**: Modern P2P topology configuration
- **Health Checks**: EKG and socket-based health monitoring
- **Resource Management**: Production-ready resource limits

#### Cardano DB Sync (`base/cardano-db-sync/`)
- **Database Integration**: Connects to PostgreSQL for blockchain data storage
- **Socket Relay**: Uses socat to connect to cardano-node via TCP
- **Dependency Management**: Waits for both database and node readiness

#### PostgreSQL (`base/postgres/`)
- **Optimized Configuration**: Tuned for blockchain data workloads
- **Persistent Storage**: StatefulSet with persistent volumes
- **Health Monitoring**: PostgreSQL-specific health checks

### Network Patches

#### Preprod Network (`base/network-specific-patches/preprod/`)
- **Environment Variables**: Preprod-specific environment configuration
- **Container Images**: Specific image versions for preprod
- **Network Configuration**: Preprod network magic and endpoints

### Overlays

#### Preprod Base (`overlays/preprod-base/`)
- **Minimal Deployment**: Cardano node only
- **Development/Testing**: Suitable for node-only testing

#### Preprod Full (`overlays/preprod-full/`)
- **Complete Stack**: Node + DB Sync + PostgreSQL
- **Production Ready**: Full blockchain infrastructure

## Deployment

### Prerequisites
- Kubernetes cluster with at least 16GB RAM and 500GB storage
- kubectl configured to access your cluster
- kustomize CLI tool (or kubectl with built-in kustomize)

### Quick Start

```bash
# Deploy the complete preprod stack
kubectl apply -k overlays/preprod-full/

# Or deploy just the node for testing
kubectl apply -k overlays/preprod-base/
```

### Monitoring Deployment

```bash
# Check pod status
kubectl get pods -n cardano-preprod

# Monitor logs
kubectl logs -f statefulset/cardano-node -n cardano-preprod
kubectl logs -f statefulset/cardano-db-sync -n cardano-preprod -c cardano-db-sync

# Check storage usage
kubectl get pvc -n cardano-preprod
```

## Features

### ✅ **Mithril Fast Sync**
- Automated Mithril client download and configuration
- Fast blockchain state restoration for preprod network
- Configurable snapshot restoration in init containers

### ✅ **Production-Ready Architecture**
- StatefulSets with persistent storage for all stateful components
- Proper resource limits and health checks
- Init containers for dependency management and setup

### ✅ **Modular Design**
- Clean separation between base components and environment-specific patches
- Reusable components following Kustomize best practices
- Easy to extend for additional networks (mainnet, testnet, etc.)

### ✅ **Advanced Networking**
- Socket relay containers for cardano-node ↔ cardano-db-sync communication
- Headless services for StatefulSet networking
- Proper port exposure for monitoring and metrics

## Customization

### Adding New Networks

1. Create network-specific patches:
```bash
mkdir -p base/network-specific-patches/mainnet/patches
```

2. Create component kustomization:
```yaml
# base/network-specific-patches/mainnet/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component
patches:
- path: patches/cardano-node-deployment-env.yaml
  target:
    kind: StatefulSet
    name: cardano-node
```

3. Create overlay:
```yaml
# overlays/mainnet-full/kustomization.yaml
resources:
- ../../base/common
- ../../base/cardano-node
- ../../base/cardano-db-sync
- ../../base/postgres
components:
- ../../base/network-specific-patches/mainnet
```

### Modifying Resources

The modular structure allows easy customization:

- **Resource Limits**: Edit StatefulSet manifests in base components
- **Storage Sizes**: Modify volumeClaimTemplates in StatefulSets
- **Network Configuration**: Update patches in network-specific-patches
- **Environment Variables**: Modify common-env.yaml

## Resource Requirements

### Minimum Requirements
- **Total CPU**: 6 cores
- **Total RAM**: 16GB
- **Total Storage**: 500GB

### Recommended for Production
- **Total CPU**: 12 cores
- **Total RAM**: 32GB
- **Total Storage**: 1TB

### Per Component
| Component | CPU Request | CPU Limit | Memory Request | Memory Limit | Storage |
|-----------|-------------|-----------|----------------|--------------|---------|
| PostgreSQL | 1000m | 2000m | 2Gi | 4Gi | 200Gi |
| Cardano Node | 2000m | 4000m | 4Gi | 8Gi | 100Gi |
| Cardano DB Sync | 2000m | 4000m | 4Gi | 8Gi | 150Gi |

## Monitoring

### Health Checks
- **PostgreSQL**: `pg_isready` checks every 10 seconds
- **Cardano Node**: EKG endpoint health check every 60 seconds
- **Cardano DB Sync**: Database connectivity check every 60 seconds

### Metrics Endpoints
- **Cardano Node EKG**: `http://cardano-node:12781`
- **Cardano Node Prometheus**: `http://cardano-node:13788/metrics`
- **Cardano DB Sync Prometheus**: `http://cardano-db-sync:8080/metrics`

## Troubleshooting

### Common Issues

1. **Pods Stuck in Pending**
   ```bash
   kubectl describe pod <pod-name> -n cardano-preprod
   # Check events for storage or resource issues
   ```

2. **Mithril Download Failures**
   ```bash
   kubectl logs cardano-node-0 -n cardano-preprod -c mithril-restore
   # Check network connectivity and Mithril endpoint
   ```

3. **Database Connection Issues**
   ```bash
   kubectl logs postgres-0 -n cardano-preprod
   kubectl get secret postgres-secret -n cardano-preprod -o yaml
   ```

### Useful Commands

```bash
# Access PostgreSQL
kubectl exec -it postgres-0 -n cardano-preprod -- psql -U postgres -d cexplorer

# Check cardano-node socket
kubectl exec -it cardano-node-0 -n cardano-preprod -- ls -la /ipc/

# Monitor resource usage
kubectl top pods -n cardano-preprod

# Scale components
kubectl scale statefulset cardano-db-sync --replicas=0 -n cardano-preprod
```

## Security Considerations

- **Change Default Passwords**: Update postgres-secret.yaml with secure credentials
- **Network Policies**: Consider implementing network policies for pod-to-pod communication
- **RBAC**: Implement proper role-based access control
- **Image Security**: Regular security updates for container images

## Cleanup

```bash
# Remove all resources
kubectl delete namespace cardano-preprod

# Or remove specific overlay
kubectl delete -k overlays/preprod-full/
```

## Comparison with Original Manifests

This Kustomize structure provides the same functionality as the original `cardano-k8s-manifests/` but with:

- **Modular Organization**: Components can be reused across environments
- **Environment Separation**: Clear distinction between base and environment-specific configs
- **Maintainability**: Easier to update and extend for new networks
- **Best Practices**: Follows established Kustomize and Kubernetes patterns
- **Scalability**: Easy to add new components and environments
