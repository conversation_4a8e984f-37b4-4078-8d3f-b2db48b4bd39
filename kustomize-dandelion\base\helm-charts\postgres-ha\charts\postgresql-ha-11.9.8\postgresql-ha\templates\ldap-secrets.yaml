{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.ldap.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "postgresql-ha.ldap" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  bind-password: {{ (include "postgresql-ha.ldapPassword" .) | b64enc | quote }}
{{- end -}}
