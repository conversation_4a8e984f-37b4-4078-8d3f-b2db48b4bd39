apiVersion: batch/v1
kind: Job
metadata:
  name: deploy-koios-sql
spec:
  backoffLimit: 5
  template:
    spec:
      serviceAccountName: koios-deploy-sql
      containers:
      - name: deploy-koios-sql
        command: ["bash", "-x", "/configmap/koios-deploy-sql-entrypoint"]
        #image: gimbalabs/cardano-db-sync-init-container:1.35.3-0
        image: repsistance/cardano-node:iohk-preprod-passive-8.1.1-0
        volumeMounts:
        - name: koios-configmap
          mountPath: /configmap
        - name: cardano-node-configmap
          mountPath: /cardano-node-configmap
        env:
        - name: KOIOS_REPOSITORY
          value: cardano-community/guild-operators
        - name: KOIOS_BRANCH
          value: alpha
        - name: KOIOS_VERSION
          value: "1.0.10"
          #value: "420"
        - name: CNODE_HOME
          value: /opt/cardano/cnode
        - name: USESYSVARS
          value: Y
        - name: CNODE_CONFIG_FILE
          value: /cardano-node-configmap/config.json
        - name: PGHOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RW
        - name: PGPORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: PGDATABASE
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: PGUSER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-postgresql
              key: password
        - name: POSTGRES_USER_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: usernames
        - name: POSTGRES_PASSWORD_RO
          valueFrom:
            secretKeyRef:
              name: init0-postgresql-ha-pgpool-custom-users
              key: passwords
      restartPolicy: Never
      volumes:
      - name: koios-configmap
        configMap:
          name: koios-configmap
      - name: cardano-node-configmap
        configMap:
          name: cardano-node

