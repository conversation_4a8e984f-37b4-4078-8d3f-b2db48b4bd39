apiVersion: v1
kind: Service
metadata:
  labels:
    io.kompose.service: cardano-node
  name: cardano-node
spec:
  ports:
  - name: "3001"
    port: 3001
    targetPort: 3001
  - name: "12798"
    port: 12798
    targetPort: 12798
  - name: "30000"
    port: 30000
    targetPort: 30000
  - name: "40000"
    port: 40000
    targetPort: 40000
  selector:
    io.kompose.service: cardano-node
---
apiVersion: v1
kind: Service
metadata:
  labels:
    io.kompose.service: cardano-node
  name: cardano-node-headless
spec:
  clusterIP: None
  ports:
  - name: "3001"
    port: 3001
    targetPort: 3001
  - name: "12798"
    port: 12798
    targetPort: 12798
  - name: "30000"
    port: 30000
    targetPort: 30000
  - name: "40000"
    port: 40000
    targetPort: 40000
  selector:
    io.kompose.service: cardano-node
