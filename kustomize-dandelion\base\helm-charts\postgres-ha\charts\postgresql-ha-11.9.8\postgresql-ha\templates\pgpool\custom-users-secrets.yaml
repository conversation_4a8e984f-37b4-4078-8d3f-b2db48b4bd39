{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and (not .Values.pgpool.customUsersSecret) .Values.pgpool.customUsers.usernames }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-custom-users" (include "postgresql-ha.pgpool" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  {{- $versionLabel := dict "app.kubernetes.io/version" ( include "common.images.version" ( dict "imageRoot" .Values.pgpool.image "chart" .Chart ) ) }}
  {{- $labels := include "common.tplvalues.merge" ( dict "values" ( list .Values.commonLabels $versionLabel ) "context" . ) }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" $labels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: pgpool
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  usernames: {{ .Values.pgpool.customUsers.usernames | b64enc | quote }}
  passwords: {{ .Values.pgpool.customUsers.passwords | b64enc | quote }}
{{- end -}}
