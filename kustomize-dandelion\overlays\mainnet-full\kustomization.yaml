commonLabels:
  environment: production
  cardano_network: mainnet
  cardano_node_type: haskell
  project_name: dandelion
commonAnnotations:
  note: free-apis!

namespace: dandelion-mainnet

bases:
- ../mainnet-base
- ../../base/helm-charts/postgres-ha
- ../../base/cardano-db-sync
- ../../base/cardano-graphql
- ../../base/cardano-hasura
- ../../base/cardano-rosetta
- ../../base/cardano-submit-api
- ../../base/cardano-explorer-api
- ../../base/cardano-token-registry
#- ../../base/metrics-exporter
- ../../base/ogmios
- ../../base/postgrest
- ../../base/koios
- ../../base/blockfrost
- ../../base/kupo

resources:
- resources/ingress.yaml

components:
- ../../base/network-specific-patches/mainnet
