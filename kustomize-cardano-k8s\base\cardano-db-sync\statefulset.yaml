apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-db-sync
  labels:
    io.kompose.service: cardano-db-sync
spec:
  serviceName: cardano-db-sync
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: cardano-db-sync
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        io.kompose.service: cardano-db-sync
    spec:
      initContainers:
      # Setup node configuration files
      - name: setup-node-config
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        imagePullPolicy: IfNotPresent
        command: ["bash", "/configmap/initContainer-setup-node-config-entrypoint"]
        volumeMounts:
        - name: cardano-node-config
          mountPath: /config
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      
      # Restore from snapshot if needed
      - name: restore-snapshot
        image: ghcr.io/intersectmbo/cardano-db-sync:********
        imagePullPolicy: IfNotPresent
        command: ["bash", "-x", "/configmap/initContainer-restore-snapshot-entrypoint"]
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        env:
        - name: NETWORK
          value: preprod
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        - name: SOCAT_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: SOCAT_TIMEOUT
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: RESTORE_SNAPSHOT
          value: "false"  # Set to true if you have preprod snapshots available
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RW
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        volumeMounts:
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        - name: dbsync-statedir
          mountPath: /db-sync-statedir
        - name: node-ipc
          mountPath: /aux-data-dir
        - name: node-ipc
          mountPath: /ipc
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      
      containers:
      # Main cardano-db-sync container
      - name: cardano-db-sync
        image: ghcr.io/intersectmbo/cardano-db-sync:********
        imagePullPolicy: IfNotPresent
        workingDir: /var/lib/cexplorer
        command: ["sh", "-x", "/configmap/entrypoint"]
        env:
        - name: NETWORK
          value: preprod
        - name: CARDANO_DB_SYNC_STATE_DIR
          value: /var/lib/cexplorer
        - name: CARDANO_NODE_SOCKET_PATH
          value: /node-ipc/node.socket
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 8080
          name: prometheus
        volumeMounts:
        - name: node-ipc
          mountPath: /node-ipc
        - name: node-ipc
          mountPath: /tmp  # Workaround for missing tmp dir
        - name: dbsync-statedir
          mountPath: /var/lib/cexplorer
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        - name: cardano-node-config
          mountPath: /opt/cardano/cnode/files
        resources:
          requests:
            memory: "4Gi"
            cpu: "2000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        livenessProbe:
          exec:
            command: ["bash", "/configmap/liveness-healthcheck"]
          initialDelaySeconds: 600
          periodSeconds: 60
          timeoutSeconds: 120
          failureThreshold: 3
      
      # Socket relay container to connect to cardano-node
      - name: socat-socket-server
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_HOST
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        - name: SOCAT_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: SOCAT_TIMEOUT
        command:
        - sh
        - -c
        - rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: cardano-node-config
        emptyDir: {}
      - name: cardano-db-sync-configmap
        configMap:
          name: cardano-db-sync-configmap
          defaultMode: 0755
      
      restartPolicy: Always
  
  volumeClaimTemplates:
  - metadata:
      name: dbsync-statedir
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 150Gi
