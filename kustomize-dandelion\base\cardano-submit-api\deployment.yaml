apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: cardano-submit-api
  name: cardano-submit-api
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: cardano-submit-api
  template:
    metadata:
      labels:
        io.kompose.service: cardano-submit-api
    spec:
      initContainers:
      - name: configure
        command: ["bash", "/configmap/initContainer-entrypoint"]
        image: repsistance/cardano-node:iohk-mn-passive-8.1.2-0
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: cardano-submit-api-config
          mountPath: /config
        - name: cardano-submit-api-configmap
          mountPath: /configmap
      containers:
      - name: cardano-submit-api
        image: inputoutput/cardano-submit-api:8.1.1
        command: ["sh", "-x", "/configmap/entrypoint"]
        env:
        - name: CARDANO_SUBMIT_API_CONFIG_PATH
          value: "/configmap/config.json"
        - name: CARDANO_NODE_SOCKET_PATH
          value: "/node-ipc/node.socket"
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8090
        resources: {}
        volumeMounts:
        - mountPath: /node-ipc
          name: node-ipc
        - name: cardano-submit-api-configmap
          mountPath: /configmap
        - name: cardano-submit-api-config
          mountPath: /config
      - name: socat-socket-server
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        command: ["sh", "-c", "rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: ""
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: cardano-submit-api-configmap
        configMap:
          name: cardano-submit-api-configmap
      - name: cardano-submit-api-config
        emptyDir: {}
