apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-db-sync-config
  namespace: cardano-preprod
data:
  # Setup node configuration for db-sync
  initContainer-setup-node-config-entrypoint: |
    #!/bin/bash
    set -euo pipefail
    
    echo "Setting up node configuration for cardano-db-sync..."
    
    # Create config directory
    mkdir -p /config
    
    # Download preprod genesis files
    echo "Downloading preprod network configuration files..."
    curl -o /config/byron-genesis.json https://book.world.dev.cardano.org/environments/preprod/byron-genesis.json
    curl -o /config/genesis.json https://book.world.dev.cardano.org/environments/preprod/shelley-genesis.json
    curl -o /config/alonzo-genesis.json https://book.world.dev.cardano.org/environments/preprod/alonzo-genesis.json
    curl -o /config/conway-genesis.json https://book.world.dev.cardano.org/environments/preprod/conway-genesis.json
    
    echo "Network configuration files downloaded successfully"

  # Snapshot restoration for db-sync (using Mithril or S3 snapshots)
  initContainer-restore-snapshot-entrypoint: |
    #!/bin/bash
    set -euo pipefail
    
    echo "Starting cardano-db-sync snapshot restoration for preprod..."
    
    # Wait for cardano-node to be ready
    echo "Waiting for cardano-node socket..."
    timeout=300
    while [ $timeout -gt 0 ]; do
      if socat -T1 - TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT} < /dev/null 2>/dev/null; then
        echo "Cardano node is ready"
        break
      fi
      echo "Waiting for cardano-node... ($timeout seconds remaining)"
      sleep 5
      timeout=$((timeout - 5))
    done
    
    if [ $timeout -le 0 ]; then
      echo "Timeout waiting for cardano-node"
      exit 1
    fi
    
    # Check if we should restore from snapshot
    if [[ "${RESTORE_SNAPSHOT}" == "true" ]]; then
      echo "Restoring cardano-db-sync from snapshot..."
      
      # Create database if it doesn't exist
      PGPASSWORD=${PGPASSWORD} psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -tc "SELECT 1 FROM pg_database WHERE datname = '${POSTGRES_DB}'" | grep -q 1 || \
      PGPASSWORD=${PGPASSWORD} psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -c "CREATE DATABASE ${POSTGRES_DB};"
      
      # For preprod, we can use available snapshots or start fresh
      echo "DB-sync will start syncing from the current cardano-node state"
      echo "Snapshot restoration completed"
    fi

  # Main db-sync entrypoint
  entrypoint: |
    #!/bin/bash
    set -euo pipefail
    
    echo "Starting cardano-db-sync for preprod network..."
    
    # Wait for database to be ready
    until PGPASSWORD=${POSTGRES_PASSWORD} psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c '\q'; do
      echo "Waiting for database..."
      sleep 5
    done
    
    # Wait for cardano-node socket
    until [ -S ${CARDANO_NODE_SOCKET_PATH} ]; do
      echo "Waiting for cardano-node socket..."
      sleep 10
    done
    
    echo "Starting cardano-db-sync..."
    
    # Run cardano-db-sync
    exec cardano-db-sync \
      --config /opt/cardano/cnode/files/config.json \
      --socket-path ${CARDANO_NODE_SOCKET_PATH} \
      --state-dir ${CARDANO_DB_SYNC_STATE_DIR} \
      --schema-dir /opt/cardano/db-sync/schema/

  # Health check script
  liveness-healthcheck: |
    #!/bin/bash
    # Simple liveness check - verify db-sync is running and database is accessible
    PGPASSWORD=${POSTGRES_PASSWORD} psql -h ${POSTGRES_HOST} -p ${POSTGRES_PORT} -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "SELECT 1;" > /dev/null 2>&1

  # DB-sync specific configuration
  cardano-db-sync-config.json: |
    {
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "NetworkName": "preprod",
      "NodeConfigFile": "/opt/cardano/cnode/files/config.json",
      "PrometheusPort": 8080,
      "RequiresNetworkMagic": "RequiresMagic",
      "defaultBackends": ["KatipBK"],
      "defaultScribes": [["StdoutSK", "stdout"]],
      "minSeverity": "Info",
      "options": {
        "cfokey": {
          "value": "Release-1.0.0"
        },
        "mapBackends": {},
        "mapSeverity": {
          "db-sync-node": "Info",
          "db-sync-node.Mux": "Error",
          "db-sync-node.Subscription": "Error"
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 10000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": ["KatipBK"],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
