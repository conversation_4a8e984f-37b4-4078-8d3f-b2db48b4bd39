{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.witness.create }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-witness" (include "postgresql-ha.postgresql" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
  {{- if or .Values.commonAnnotations .Values.service.headless.annotations }}
  {{- $annotations := include "common.tplvalues.merge" ( dict "values" ( list .Values.service.headless.serviceAnnotations .Values.service.annotations .Values.commonAnnotations ) "context" . ) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: {{ .Values.service.portName | quote }}
      port: {{ .Values.postgresql.containerPorts.postgresql }}
      targetPort: postgresql
      protocol: TCP
  {{- $podLabels := include "common.tplvalues.merge" ( dict "values" ( list .Values.witness.podLabels .Values.commonLabels ) "context" . ) }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: postgresql
    role: witness
{{- end }}
