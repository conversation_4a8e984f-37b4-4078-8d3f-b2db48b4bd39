apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-node
data:
  initContainer-entrypoint: |
    #!/bin/bash
    set -euo pipefail
    
    # Mithril client configuration for fast sync
    export MITHRIL_AGGREGATOR_ENDPOINT="https://aggregator.testing-preprod.api.mithril.network/aggregator"
    export MITHRIL_GENESIS_VERIFICATION_KEY="5b3132372c37332c3132342c3136312c362c3133372c3133312c3231332c3230372c3131372c3139382c38352c3137362c3139392c3136322c3234312c36382c3132332c3131392c3134372c3133352c3138352c3161312c3138392c3134342c3138392c3135332c3136352c3139392c3135305d"
    
    # Check if database already exists and is not empty
    if [[ -d "/data/db" ]] && [[ "$(ls -A /data/db 2>/dev/null)" ]]; then
      echo "Database directory exists and is not empty, skipping restoration"
      exit 0
    fi
    
    # Create data directory if it doesn't exist
    mkdir -p /data/db
    
    # Use Mithril client to restore snapshot for fast sync
    if [[ "${RESTORE_SNAPSHOT}" == "true" ]]; then
      echo "Starting Mithril snapshot restoration for preprod network..."
      
      # Download and install mithril-client
      curl -sSL https://github.com/input-output-hk/mithril/releases/latest/download/mithril-client-linux-x64.tar.gz | tar -xz -C /tmp
      chmod +x /tmp/mithril-client
      
      # Restore from Mithril snapshot
      /tmp/mithril-client cardano-db download latest --download-dir /data/db
      
      echo "Mithril snapshot restoration completed"
    fi
  topology.json: |
    {
       "Producers": [
          {
            "addr": "preprod-node.world.dev.cardano.org",
            "port": 30000,
            "valency": 2
          },
          {
            "addr": "preprod.cardano-testnet.iohkdev.io",
            "port": 3001,
            "valency": 1
          }
       ]
     }
  config.json: |
    {
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 1,
      "AlonzoGenesisFile": "/opt/cardano/cnode/files/alonzo-genesis.json",
      "AlonzoGenesisHash": "7e94a15f55d1e82d10f09203fa1d40f8eede58fd8066542cf6566008068ed874",
      "ByronGenesisFile": "/opt/cardano/cnode/files/byron-genesis.json",
      "ByronGenesisHash": "d4b8de7a11d929a323373cbab6c1a9bdc931beffff11db111cf9d57356ee1937",
      "LastKnownBlockVersion-Alt": 0,
      "LastKnownBlockVersion-Major": 3,
      "LastKnownBlockVersion-Minor": 0,
      "MaxKnownMajorProtocolVersion": 8,
      "PBftSignatureThreshold": 0.9,
      "NetworkName": "preprod",
      "MaxConcurrencyDeadline": 2,
      "MempoolCapacityBytesOverride": 256000000,
      "NumCoreNodes": 2,
      "Protocol": "Cardano",
      "RequiresNetworkMagic": "RequiresMagic",
      "ShelleyGenesisFile": "/opt/cardano/cnode/files/genesis.json",
      "ShelleyGenesisHash": "d1c495872896cd50a6b6fb75ba6a3c37936a13d06796f6a96c6b4c2c1b138575",
      "ConwayGenesisFile": "/opt/cardano/cnode/files/conway-genesis.json",
      "ConwayGenesisHash": "f28f1c1280ea0d32f8cd3143e268650d6c1a8e221522ce4a7d20d62fc09783e1",
      "SocketPath": "/opt/cardano/cnode/sockets/node0.socket",
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "TraceBlockFetchClient": true,
      "TraceBlockFetchDecisions": true,
      "TraceBlockFetchProtocol": true,
      "TraceBlockFetchProtocolSerialised": true,
      "TraceBlockFetchServer": true,
      "TraceChainDb": true,
      "TraceChainSyncBlockServer": true,
      "TraceChainSyncClient": true,
      "TraceChainSyncHeaderServer": true,
      "TraceChainSyncProtocol": true,
      "TraceDNSResolver": false,
      "TraceDNSSubscription": false,
      "TraceErrorPolicy": true,
      "TraceForge": true,
      "TraceHandshake": true,
      "TraceIpSubscription": true,
      "TraceLocalChainSyncProtocol": true,
      "TraceLocalErrorPolicy": true,
      "TraceLocalHandshake": false,
      "TraceLocalTxSubmissionProtocol": true,
      "TraceLocalTxSubmissionServer": true,
      "TraceMempool": true,
      "TraceMux": false,
      "TraceTxInbound": true,
      "TraceTxOutbound": true,
      "TraceTxSubmissionProtocol": true,
      "TracingVerbosity": "MaximalVerbosity",
      "TurnOnLogMetrics": false,
      "TurnOnLogging": true,
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "hasEKG": 12788,
      "hasPrometheus": [
        "0.0.0.0",
        12798
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node.metrics.Forge": [
             "EKGViewBK"
          ],
          "cardano.node.metrics": [
            "EKGViewBK"
          ],
          "cardano.node.resources": [
            "EKGViewBK"
          ]
        },
        "mapSubtrace": {
          "cardano.node.metrics": {
            "subtrace": "Neutral"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 10000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "KatipBK",
        "EKGViewBK"
      ],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
