- op: replace
  path: /data/topology.json.passive
  value: |
    {
      "LocalRoots": {
        "groups": [
          {
            "localRoots": {
              "accessPoints": [],
              "advertise": false
            },
            "valency": 1
          }
        ]
      },
      "PublicRoots": [
        {
          "publicRoots": {
            "accessPoints": [
              {
                "address": "preprod-node.world.dev.cardano.org",
                "port": 30000
              }
            ],
            "advertise": false
          }
        }
      ],
      "useLedgerAfterSlot": 4642000
    }

- op: add
  path: /data/config.json
  value: |
    {
      "AlonzoGenesisFile": "/opt/cardano/cnode/files/alonzo-genesis.json",
      "AlonzoGenesisHash": "7e94a15f55d1e82d10f09203fa1d40f8eede58fd8066542cf6566008068ed874",
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 0,
      "ByronGenesisFile": "/opt/cardano/cnode/files/byron-genesis.json",
      "ByronGenesisHash": "d4b8de7a11d929a323373cbab6c1a9bdc931beffff11db111cf9d57356ee1937",
      "ConwayGenesisFile": "/opt/cardano/cnode/files/conway-genesis.json",
      "ConwayGenesisHash": "f28f1c1280ea0d32f8cd3143e268650d6c1a8e221522ce4a7d20d62fc09783e1",
      "EnableP2P": true,
      "LastKnownBlockVersion-Alt": 0,
      "LastKnownBlockVersion-Major": 2,
      "LastKnownBlockVersion-Minor": 0,
      "Protocol": "Cardano",
      "RequiresNetworkMagic": "RequiresMagic",
      "ShelleyGenesisFile": "/opt/cardano/cnode/files/genesis.json",
      "ShelleyGenesisHash": "162d29c4e1cf6b8a84f2d692e67a3ac6bc7851bc3e6e4afe64d15778bed8bd86",
      "TargetNumberOfActivePeers": 20,
      "TargetNumberOfEstablishedPeers": 50,
      "TargetNumberOfKnownPeers": 100,
      "TargetNumberOfRootPeers": 100,
      "TraceAcceptPolicy": true,
      "TraceBlockFetchClient": false,
      "TraceBlockFetchDecisions": false,
      "TraceBlockFetchProtocol": false,
      "TraceBlockFetchProtocolSerialised": false,
      "TraceBlockFetchServer": false,
      "TraceChainDb": true,
      "TraceChainSyncBlockServer": false,
      "TraceChainSyncClient": false,
      "TraceChainSyncHeaderServer": false,
      "TraceChainSyncProtocol": false,
      "TraceConnectionManager": true,
      "TraceDNSResolver": true,
      "TraceDNSSubscription": true,
      "TraceDiffusionInitialization": true,
      "TraceErrorPolicy": true,
      "TraceForge": true,
      "TraceHandshake": false,
      "TraceInboundGovernor": true,
      "TraceIpSubscription": true,
      "TraceLedgerPeers": true,
      "TraceLocalChainSyncProtocol": false,
      "TraceLocalErrorPolicy": true,
      "TraceLocalHandshake": false,
      "TraceLocalRootPeers": true,
      "TraceLocalTxSubmissionProtocol": false,
      "TraceLocalTxSubmissionServer": false,
      "TraceMempool": true,
      "TraceMux": false,
      "TracePeerSelection": true,
      "TracePeerSelectionActions": true,
      "TracePublicRootPeers": true,
      "TraceServer": true,
      "TraceTxInbound": false,
      "TraceTxOutbound": false,
      "TraceTxSubmissionProtocol": false,
      "TracingVerbosity": "NormalVerbosity",
      "TurnOnLogMetrics": true,
      "TurnOnLogging": true,
      "ViewMode": "SimpleView",
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "hasEKG": 12781,
      "hasPrometheus": [
        "0.0.0.0",
        13788
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node-metrics": [
            "EKGViewBK",
            {
              "kind": "UserDefinedBK",
              "name": "LiveViewBackend"
            }
          ],
          "cardano.node.BlockFetchDecision.peers": [
            "EKGViewBK",
            {
              "kind": "UserDefinedBK",
              "name": "LiveViewBackend"
            }
          ],
          "cardano.node.ChainDB.metrics": [
            "EKGViewBK",
            {
              "kind": "UserDefinedBK",
              "name": "LiveViewBackend"
            }
          ],
          "cardano.node.Forge.metrics": [
            "EKGViewBK"
          ],
          "cardano.node.metrics": [
            "EKGViewBK",
            {
              "kind": "UserDefinedBK",
              "name": "LiveViewBackend"
            }
          ],
          "cardano.node.metrics.Forge": [
            "EKGViewBK"
          ],
          "cardano.node.metrics.Mempool": [
            "EKGViewBK"
          ],
          "cardano.node.peers": [
            "EKGViewBK"
          ]
        },
        "mapSubtrace": {
          "#ekgview": {
            "contents": [
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": ".monoclock.basic.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "cardano.epoch-validation.benchmark",
                  "tag": "Contains"
                },
                [
                  {
                    "contents": "diff.RTS.cpuNs.timed.",
                    "tag": "Contains"
                  }
                ]
              ],
              [
                {
                  "contents": "#ekgview.#aggregation.cardano.epoch-validation.benchmark",
                  "tag": "StartsWith"
                },
                [
                  {
                    "contents": "diff.RTS.gcNum.timed.",
                    "tag": "Contains"
                  }
                ]
              ]
            ],
            "subtrace": "FilterTrace"
          },
          "benchmark": {
            "contents": [
              "GhcRtsStats",
              "MonotonicClock"
            ],
            "subtrace": "ObservableTrace"
          },
          "cardano.epoch-validation.utxo-stats": {
            "subtrace": "NoTrace"
          },
          "cardano.node-metrics": {
            "subtrace": "Neutral"
          },
          "cardano.node.metrics": {
            "subtrace": "Neutral"
          }
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 10000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": [
        "KatipBK",
        "EKGViewBK"
      ],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
