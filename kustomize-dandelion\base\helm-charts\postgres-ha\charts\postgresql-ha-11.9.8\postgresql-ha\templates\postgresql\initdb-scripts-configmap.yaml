{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.postgresql.initdbScripts (not .Values.postgresql.initdbScriptsCM) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-initdb-scripts" (include "postgresql-ha.postgresql" .) }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: postgresql
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  {{- include "common.tplvalues.render" (dict "value" .Values.postgresql.initdbScripts "context" $) | nindent 4 }}
{{- end }}
