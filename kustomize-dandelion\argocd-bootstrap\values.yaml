git:
  repositoryUrl: https://gitlab.com/gimbalabs/dandelion/kustomize-dandelion.git
  targetRevision: HEAD

dandelion:
  provider:
    name: null
  argoProjectSuffix: "-v12-0-0"
  argoAppSuffix: "-v12-0-0"
  namespaceSuffix: "-v12-0-0"
  ingressBaseDomain: null
  ingressAlternativeDomain: null
  multiNode: false
  mainnet:
    enabled: true
    db:
      adminPassword: "CHANGEME"
      roPassword: "CHANGEME"
      repmgrPassword: "CHANGEME"
      diskSize: "60Gi"
  testnet:
    enabled: false
    db:
      adminPassword: "CHANGEME"
      roPassword: "CHANGEME"
      repmgrPassword: "CHANGEME"
      diskSize: "60Gi"

argo-cd:
  enabled: true
  server:
    extraArgs:
      - --insecure
    ingress:
      enabled: true
      hosts:
        - argocd.local
    config:
      repositories: |
        - url: https://gitlab.com/gimbalabs/dandelion/kustomize-dandelion.git
          name: kustomize-dandelion
        - url:  https://charts.bitnami.com/bitnami
          name: bitnami
          type: helm
        - url:  https://prometheus-community.github.io/helm-charts
          name: prometheus-community
          type: helm
        - url:  https://grafana.github.io/helm-charts
          name: grafana
          type: helm
        - url:  https://helm.traefik.io/traefik
          name: traefik
          type: helm
      configManagementPlugins: |
        - name: kustomized-kustomize-network
          generate:
            command: ["/bin/sh", "-c"]
            args: ["if [ \"${MULTI_NODE_AFFINITY}\" == \"true\" ]; then echo \"- ../../${CARDANO_NETWORK}-affinity-patches\" >> ../../base/helpers/${CARDANO_NETWORK}-patcher/kustomization.yaml; fi ; kustomize build > ../../base/helpers/${CARDANO_NETWORK}-patcher/input.yaml && kustomize build ../../base/helpers/${CARDANO_NETWORK}-patcher"]
        - name: kustomized-kustomize-ipfs-cluster
          generate:
            command: ["/bin/bash", "-c"]
            args: ["source baids/99-misc && kustomize-ipfs-generate-ipfs-key"]

      resource.customizations: |
        networking.k8s.io/Ingress:
          health.lua: |
            hs = {}
            hs.status = "Healthy"
            return hs
