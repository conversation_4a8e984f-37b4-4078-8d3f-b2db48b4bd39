apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    io.kompose.service: cardano-db-sync
  name: cardano-db-sync
spec:
  replicas: 1
  updateStrategy:
    type: OnDelete
  selector:
    matchLabels:
      io.kompose.service: cardano-db-sync
  serviceName: cardano-db-sync
  template:
    metadata:
      labels:
        io.kompose.service: cardano-db-sync
    spec:
      initContainers:
      - name: setup-node-config
        command: ["bash", "/configmap/initContainer-setup-node-config-entrypoint"]
        image: inputoutput/cardano-node:8.7.3
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: cardano-node-config
          mountPath: /config
        - name: cardano-db-sync-configmap
          mountPath: /configmap
      - name: restore-snapshot
        command: ["bash", "-x", "/configmap/initContainer-restore-snapshot-entrypoint"]
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0
        image: inputoutput/cardano-db-sync:********
        imagePullPolicy: IfNotPresent
        env:
        - name: NETWORK
          value: preprod
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST_RW
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: carnado-postgresql-ha-postgresql
              key: password
        - name: POSTGRES_USER_RO
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER_RO
        - name: POSTGRES_PASSWORD_RO
          value: "readonly_password_change_me"
        volumeMounts:
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        - name: dbsync-statedir
          mountPath: /db-sync-statedir
        - name: node-ipc
          mountPath: /aux-data-dir
        - name: node-ipc
          mountPath: /ipc
      containers:
      - name: cardano-db-sync
        env:
        - name: EXTENDED
          value: "true"
        - name: NETWORK
          value: preprod
        - name: DISABLE_EPOCH
          value: "false"
        - name: DISABLE_CACHE
          value: "false"
        - name: DISABLE_LEDGER
          value: "false"
        - name: CARDANO_DB_SYNC_STATE_DIR
          value: /var/lib/cexplorer
        - name: CARDANO_NODE_SOCKET_PATH
          value: "/node-ipc/node.socket"
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_HOST
        - name: POSTGRES_PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_PORT
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: carnado-postgresql-ha-postgresql
              key: password
        image: inputoutput/cardano-db-sync:********
        workingDir: /var/lib/cexplorer
        command: ["sh", "-x", "/configmap/entrypoint"]
        imagePullPolicy: IfNotPresent
        resources: {}
        volumeMounts:
        - name: node-ipc
          mountPath: /node-ipc
        - name: node-ipc
          mountPath: /tmp
        - name: common-env
          mountPath: /run/secrets/common-env
          readOnly: true
        - name: postgres-password
          mountPath: /run/secrets/postgres-password
          readOnly: true
        - name: dbsync-statedir
          mountPath: /var/lib/cexplorer
        - name: cardano-db-sync-configmap
          mountPath: /configmap
        - name: cardano-node-config
          mountPath: /opt/cardano/cnode/files
        livenessProbe:
          exec:
            command: ["bash", "/configmap/liveness-healthcheck"]
          initialDelaySeconds: 600
          periodSeconds: 60
          timeoutSeconds: 120
      - name: socat-socket-server
        image: alpine/socat
        env:
        - name: CARDANO_NODE_SOCKET_TCP_HOST
          value: "cardano-node-headless"
        - name: CARDANO_NODE_SOCKET_TCP_PORT
          value: "30000"
        - name: SOCAT_TIMEOUT
          value: "3600"
        command: ["sh", "-c", "rm -f /ipc/node.socket; socat UNIX-LISTEN:/ipc/node.socket,fork TCP:${CARDANO_NODE_SOCKET_TCP_HOST}:${CARDANO_NODE_SOCKET_TCP_PORT},ignoreeof"]
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
      restartPolicy: Always
      serviceAccountName: carnado-service-account
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: cardano-node-config
        emptyDir: {}
      - name: common-env
        configMap:
          name: common-env
      - name: postgres-password
        secret:
          secretName: carnado-postgresql-ha-postgresql
          items:
          - key: password
            path: POSTGRES_PASSWORD
      - name: dbsync-statedir
        persistentVolumeClaim:
          claimName: dbsync-statedir
      - name: cardano-db-sync-configmap
        configMap:
          name: cardano-db-sync-configmap

  volumeClaimTemplates:
  - metadata:
      name: dbsync-statedir
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 100Gi
