apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

patches:
# cardano-node patches
- path: patches/cardano-node-deployment-env.yaml
  target:
    kind: StatefulSet
    name: cardano-node
- path: patches/cardano-node-deployment-initContainers-env.yaml
  target:
    kind: StatefulSet
    name: cardano-node
- path: patches/cardano-node-deployment-image.yaml
  target:
    kind: StatefulSet
    name: cardano-node
# cardano-db-sync patches
- path: patches/cardano-db-sync-deployment-initContainer-env.yaml
  target:
    kind: StatefulSet
    name: cardano-db-sync
- path: patches/cardano-db-sync-deployment-image.yaml
  target:
    kind: StatefulSet
    name: cardano-db-sync
