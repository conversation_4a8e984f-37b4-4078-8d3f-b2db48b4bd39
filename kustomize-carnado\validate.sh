#!/bin/bash

# Validation script for kustomize-carnado deployment
set -euo pipefail

echo "🔍 Validating kustomize-carnado deployment configuration..."

# Check if kustomize is available
if ! command -v kustomize &> /dev/null; then
    echo "❌ kustomize command not found. Please install kustomize CLI."
    exit 1
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl command not found. Please install kubectl CLI."
    exit 1
fi

echo "✅ Required tools found"

# Validate base configurations
echo "🔍 Validating base configurations..."

for base_dir in base/common base/cardano-node base/cardano-db-sync; do
    if [ -d "$base_dir" ]; then
        echo "  ✅ $base_dir exists"
        if [ -f "$base_dir/kustomization.yaml" ]; then
            echo "  ✅ $base_dir/kustomization.yaml exists"
        else
            echo "  ❌ $base_dir/kustomization.yaml missing"
            exit 1
        fi
    else
        echo "  ❌ $base_dir missing"
        exit 1
    fi
done

# Validate network patches
echo "🔍 Validating network-specific patches..."
if [ -d "base/network-specific-patches/testnet-preprod" ]; then
    echo "  ✅ testnet-preprod patches exist"
else
    echo "  ❌ testnet-preprod patches missing"
    exit 1
fi

# Validate overlay
echo "🔍 Validating overlay configuration..."
if [ -d "overlays/testnet-preprod" ]; then
    echo "  ✅ testnet-preprod overlay exists"
    if [ -f "overlays/testnet-preprod/kustomization.yaml" ]; then
        echo "  ✅ overlay kustomization.yaml exists"
    else
        echo "  ❌ overlay kustomization.yaml missing"
        exit 1
    fi
else
    echo "  ❌ testnet-preprod overlay missing"
    exit 1
fi

# Test kustomize build
echo "🔍 Testing kustomize build..."
if kustomize build overlays/testnet-preprod/ > /dev/null; then
    echo "  ✅ kustomize build successful"
else
    echo "  ❌ kustomize build failed"
    exit 1
fi

# Validate generated manifests
echo "🔍 Validating generated manifests..."
output=$(kustomize build overlays/testnet-preprod/)

# Check for required resources
required_resources=(
    "StatefulSet"
    "Service" 
    "ConfigMap"
    "Secret"
)

for resource in "${required_resources[@]}"; do
    if echo "$output" | grep -q "kind: $resource"; then
        echo "  ✅ $resource found in output"
    else
        echo "  ❌ $resource missing from output"
        exit 1
    fi
done

# Check for carnado prefix
if echo "$output" | grep -q "name: carnado-"; then
    echo "  ✅ carnado prefix applied correctly"
else
    echo "  ❌ carnado prefix not found"
    exit 1
fi

# Check for preprod network configuration
if echo "$output" | grep -q "preprod"; then
    echo "  ✅ preprod network configuration found"
else
    echo "  ❌ preprod network configuration missing"
    exit 1
fi

# Validate with kubectl (dry-run)
echo "🔍 Testing kubectl dry-run..."
if kustomize build overlays/testnet-preprod/ | kubectl apply --dry-run=client -f - > /dev/null; then
    echo "  ✅ kubectl dry-run successful"
else
    echo "  ❌ kubectl dry-run failed"
    exit 1
fi

echo ""
echo "🎉 All validations passed!"
echo ""
echo "📋 Summary:"
echo "  - Base configurations: ✅"
echo "  - Network patches: ✅"
echo "  - Overlay configuration: ✅"
echo "  - Kustomize build: ✅"
echo "  - Generated manifests: ✅"
echo "  - Kubectl validation: ✅"
echo ""
echo "🚀 Ready to deploy with:"
echo "   kubectl apply -k overlays/testnet-preprod/"
