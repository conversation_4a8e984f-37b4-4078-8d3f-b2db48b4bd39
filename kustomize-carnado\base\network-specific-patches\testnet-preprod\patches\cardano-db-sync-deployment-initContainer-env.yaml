- op: replace
  path: /spec/template/spec/initContainers/1/env
  value:
    - name: NETWORK
      value: preprod
    - name: CARDANO_NODE_SOCKET_TCP_HOST
      value: "cardano-node-headless"
    - name: CA<PERSON><PERSON><PERSON>_NODE_SOCKET_TCP_PORT
      value: "30000"
    - name: SOCAT_TIMEOUT
      value: "3600"
    - name: CA<PERSON><PERSON><PERSON>_NODE_SOCKET_PATH
      value: /ipc/node.socket
    - name: RESTORE_SNAPSHOT
      value: "true"
    - name: POSTGRES_HOST
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_HOST_RW
    - name: POSTGRES_PORT
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_PORT
    - name: POSTGRES_DB
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_DB
    - name: POSTGRES_USER
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_USER
    - name: PGPASSWORD
      valueFrom:
        secretKeyRef:
          name: carnado-postgresql-ha-postgresql
          key: password
    - name: POSTGRES_USER_RO
      valueFrom:
        configMapKeyRef:
          name: common-env
          key: POSTGRES_USER_RO
    - name: POSTGRES_PASSWORD_RO
      value: "readonly_password_change_me"
