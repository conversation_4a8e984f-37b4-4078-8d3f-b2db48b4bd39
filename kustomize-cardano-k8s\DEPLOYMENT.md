# Deployment Guide - Kustomize Cardano K8s

This guide provides step-by-step instructions for deploying the Cardano infrastructure using the modular Kustomize structure.

## Quick Deployment

### Option 1: Full Stack (Recommended)
```bash
# Deploy complete Cardano infrastructure (Node + DB Sync + PostgreSQL)
kubectl apply -k kustomize-cardano-k8s/overlays/preprod-full/

# Monitor deployment
kubectl get pods -n cardano-preprod -w
```

### Option 2: Node Only (Testing)
```bash
# Deploy only Cardano node for testing
kubectl apply -k kustomize-cardano-k8s/overlays/preprod-base/

# Monitor deployment
kubectl get pods -n cardano-preprod -w
```

## Detailed Deployment Steps

### Step 1: Prerequisites Check

Ensure you have:
- Kubernetes cluster with sufficient resources (16GB RAM, 500GB storage)
- kubectl configured and connected to your cluster
- kustomize CLI (or kubectl with built-in kustomize support)

```bash
# Check cluster connection
kubectl cluster-info

# Check available resources
kubectl get nodes
kubectl describe nodes | grep -A 5 "Allocatable"

# Verify storage class
kubectl get storageclass
```

### Step 2: Validate Configuration

```bash
# Test kustomize build (dry run)
kustomize build kustomize-cardano-k8s/overlays/preprod-full/

# Validate with kubectl (dry run)
kubectl apply -k kustomize-cardano-k8s/overlays/preprod-full/ --dry-run=client
```

### Step 3: Deploy Infrastructure

```bash
# Deploy the complete stack
kubectl apply -k kustomize-cardano-k8s/overlays/preprod-full/

# Verify namespace creation
kubectl get namespace cardano-preprod

# Check initial pod status
kubectl get pods -n cardano-preprod
```

### Step 4: Monitor Deployment Progress

```bash
# Watch all pods come online
kubectl get pods -n cardano-preprod -w

# Check specific component logs
kubectl logs -f statefulset/postgres -n cardano-preprod
kubectl logs -f statefulset/cardano-node -n cardano-preprod
kubectl logs -f statefulset/cardano-db-sync -n cardano-preprod -c cardano-db-sync

# Monitor storage provisioning
kubectl get pvc -n cardano-preprod
```

## Expected Deployment Timeline

### Phase 1: Infrastructure Setup (0-5 minutes)
- Namespace creation
- ConfigMaps and Secrets deployment
- Services creation
- PVC provisioning

### Phase 2: Database Startup (2-5 minutes)
- PostgreSQL pod initialization
- Database schema creation
- Health checks passing

### Phase 3: Cardano Node Bootstrap (5-60 minutes)
- Genesis files download
- Mithril snapshot download (major time component)
- Node synchronization start
- Socket availability

### Phase 4: DB Sync Initialization (10-120 minutes)
- Wait for node readiness
- Database schema setup
- Initial blockchain data sync

## Monitoring and Verification

### Health Checks

```bash
# Check all pod health
kubectl get pods -n cardano-preprod

# Detailed pod information
kubectl describe pods -n cardano-preprod

# Check service endpoints
kubectl get endpoints -n cardano-preprod
```

### Component-Specific Monitoring

#### PostgreSQL
```bash
# Check PostgreSQL logs
kubectl logs postgres-0 -n cardano-preprod

# Connect to database
kubectl exec -it postgres-0 -n cardano-preprod -- psql -U postgres -d cexplorer

# Check database size
kubectl exec -it postgres-0 -n cardano-preprod -- psql -U postgres -d cexplorer -c "SELECT pg_size_pretty(pg_database_size('cexplorer'));"
```

#### Cardano Node
```bash
# Monitor node sync progress
kubectl logs -f cardano-node-0 -n cardano-preprod

# Check node socket
kubectl exec -it cardano-node-0 -n cardano-preprod -- ls -la /ipc/

# Check EKG metrics
kubectl port-forward cardano-node-0 12781:12781 -n cardano-preprod
# Then visit http://localhost:12781 in browser

# Check Prometheus metrics
kubectl port-forward cardano-node-0 13788:13788 -n cardano-preprod
# Then visit http://localhost:13788/metrics
```

#### Cardano DB Sync
```bash
# Monitor DB sync progress
kubectl logs -f cardano-db-sync-0 -n cardano-preprod -c cardano-db-sync

# Check socket relay
kubectl logs cardano-db-sync-0 -n cardano-preprod -c socat-socket-server

# Check DB sync metrics
kubectl port-forward cardano-db-sync-0 8080:8080 -n cardano-preprod
# Then visit http://localhost:8080/metrics
```

### Storage Monitoring

```bash
# Check PVC status and usage
kubectl get pvc -n cardano-preprod

# Detailed PVC information
kubectl describe pvc -n cardano-preprod

# Monitor storage growth
watch kubectl get pvc -n cardano-preprod
```

## Troubleshooting Common Issues

### Issue: Pods Stuck in Pending
```bash
# Check events
kubectl describe pod <pod-name> -n cardano-preprod

# Common causes:
# - Insufficient cluster resources
# - Storage class not available
# - Node selector constraints
```

### Issue: Mithril Download Slow/Failing
```bash
# Check init container logs
kubectl logs cardano-node-0 -n cardano-preprod -c mithril-restore

# Common solutions:
# - Check internet connectivity
# - Verify Mithril endpoint accessibility
# - Consider network proxy settings
```

### Issue: Database Connection Failures
```bash
# Check PostgreSQL status
kubectl logs postgres-0 -n cardano-preprod

# Verify secret
kubectl get secret postgres-secret -n cardano-preprod -o yaml

# Test connection
kubectl exec -it postgres-0 -n cardano-preprod -- pg_isready -U postgres
```

### Issue: Node Not Syncing
```bash
# Check node logs for errors
kubectl logs cardano-node-0 -n cardano-preprod | tail -100

# Verify topology configuration
kubectl exec -it cardano-node-0 -n cardano-preprod -- cat /configmap/topology.json

# Check network connectivity
kubectl exec -it cardano-node-0 -n cardano-preprod -- nc -zv preprod-node.world.dev.cardano.org 30000
```

## Scaling and Maintenance

### Scaling Components
```bash
# Scale down DB sync for maintenance
kubectl scale statefulset cardano-db-sync --replicas=0 -n cardano-preprod

# Scale back up
kubectl scale statefulset cardano-db-sync --replicas=1 -n cardano-preprod
```

### Updates and Patches
```bash
# Update specific component
kubectl patch statefulset cardano-node -n cardano-preprod -p '{"spec":{"template":{"spec":{"containers":[{"name":"cardano-node","image":"ghcr.io/intersectmbo/cardano-node:10.1.5"}]}}}}'

# Rolling restart
kubectl rollout restart statefulset/cardano-node -n cardano-preprod
```

### Backup Considerations
```bash
# Backup PostgreSQL data
kubectl exec postgres-0 -n cardano-preprod -- pg_dump -U postgres cexplorer > cardano-backup.sql

# Backup node data (if needed)
# Note: With Mithril, node data can be quickly restored, so backup may not be necessary
```

## Performance Optimization

### Resource Tuning
- Monitor actual resource usage with `kubectl top pods -n cardano-preprod`
- Adjust resource requests/limits in base StatefulSets
- Consider node affinity for better performance

### Storage Optimization
- Use high-performance storage classes (SSD)
- Monitor IOPS and throughput requirements
- Consider storage expansion as blockchain grows

### Network Optimization
- Ensure low-latency network connectivity
- Consider dedicated node pools for Cardano workloads
- Monitor network bandwidth usage

## Cleanup

### Partial Cleanup (Keep Data)
```bash
# Remove pods but keep data
kubectl delete statefulset --all -n cardano-preprod
kubectl delete service --all -n cardano-preprod
kubectl delete configmap --all -n cardano-preprod
kubectl delete secret --all -n cardano-preprod

# PVCs remain for data persistence
```

### Complete Cleanup
```bash
# Remove everything including data
kubectl delete namespace cardano-preprod

# Or use kustomize
kubectl delete -k kustomize-cardano-k8s/overlays/preprod-full/
```

## Next Steps

After successful deployment:

1. **Set up monitoring**: Integrate with Prometheus/Grafana for metrics
2. **Configure alerting**: Set up alerts for critical component failures
3. **Implement backup strategy**: Regular database backups
4. **Security hardening**: Network policies, RBAC, secret management
5. **Performance tuning**: Optimize based on actual usage patterns

## Support

For issues and questions:
- Check the main README.md for architecture details
- Review component-specific logs for error messages
- Consult Cardano documentation for blockchain-specific issues
- Use kubectl describe for detailed resource information
