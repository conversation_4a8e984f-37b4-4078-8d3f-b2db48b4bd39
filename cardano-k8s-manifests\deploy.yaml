# Complete Kubernetes deployment manifest for Cardano infrastructure
# This file combines all components for easy deployment
# 
# Deploy with: kubectl apply -f deploy.yaml
# 
# Components included:
# - Namespace
# - ConfigMaps (common environment, cardano-node config, cardano-db-sync config)
# - Secret (PostgreSQL password)
# - StatefulSets (PostgreSQL, Cardano Node, Cardano DB Sync)
# - Services (for inter-component communication)

---
# Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: cardano-preprod
  labels:
    name: cardano-preprod
    network: preprod
    project: cardano-infrastructure

---
# Common environment configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: common-env
  namespace: cardano-preprod
data:
  POSTGRES_HOST: postgres
  POSTGRES_HOST_RO: postgres
  POSTGRES_HOST_RW: postgres
  POSTGRES_PORT: "5432"
  POSTGRES_DB: cexplorer
  POSTGRES_USER: postgres
  NETWORK: preprod
  CARDANO_NODE_SOCKET_TCP_HOST: cardano-node-headless
  CARDANO_NODE_SOCKET_TCP_PORT: "30000"
  SOCAT_TIMEOUT: "3600"

---
# PostgreSQL password secret
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: cardano-preprod
type: Opaque
data:
  # Password: v8hlDV0yMAHHlIurYupj (base64 encoded)
  password: djhobERWMHlNQUhIbEl1cll1cGo=

---
# PostgreSQL StatefulSet
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: cardano-preprod
  labels:
    app: postgres
    component: database
    network: preprod
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        component: database
        network: preprod
    spec:
      containers:
      - name: postgres
        image: postgres:17.2-alpine
        env:
        - name: POSTGRES_LOGGING
          value: "true"
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        ports:
        - containerPort: 5432
          name: postgres
        args:
        - -c
        - maintenance_work_mem=1GB
        - -c
        - max_parallel_maintenance_workers=4
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
      restartPolicy: Always
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 200Gi

---
# PostgreSQL Service
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: cardano-preprod
  labels:
    app: postgres
    component: database
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
    name: postgres
  clusterIP: None  # Headless service for StatefulSet
