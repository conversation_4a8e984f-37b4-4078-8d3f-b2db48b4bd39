---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-cardano-submit-api
spec:
  rules:
    - host: dev.submit-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: cardano-submit-api
                port: 
                  number: 8091
            path: /
            pathType: Prefix
  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - submit-api.testnet.dandelion.link
    #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-cardano-graphql-api
spec:
  rules:
    - host: dev.graphql-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: cardano-graphql
                port: 
                  number: 3100
            path: /
            pathType: Prefix

  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - graphql-api.testnet.dandelion.link
    #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-chisel-api
spec:
  rules:
    - host: dev.chisel-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: chisel-server
                port: 
                  number: 40000
            path: /
            pathType: Prefix

  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - chisel-api.testnet.dandelion.link
    #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-ogmios-api
spec:
  rules:
    - host: dev.ogmios-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: ogmios
                port: 
                  number: 1337
            path: /
            pathType: Prefix

  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - ogmios-api.testnet.dandelion.link
    #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-postgrest-api
spec:
  rules:
    - host: dev.postgrest-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: postgrest
                port: 
                  number: 3000
            path: /
            pathType: Prefix

  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - postgrest-api.testnet.dandelion.link
    #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-rosetta-api
spec:
  rules:
    - host: dev.rosetta-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: cardano-rosetta
                port: 
                  number: 8080
            path: /
            pathType: Prefix

  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - rosetta-api.testnet.dandelion.link
    #secretName: local-tls

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-postgresws-api
spec:
  rules:
    - host: dev.postgresws-api.testnet.dandelion.link
      http:
        paths:
          - backend:
              service:
                name: postgres-ws
                port: 
                  number: 3000
            path: /
            pathType: Prefix

  # This section is only required if TLS is to be enabled for the Ingress
  tls:
  - hosts:
    - postgresws-api.testnet.dandelion.link
    #secretName: local-tls
#cardano-node-headless                     ClusterIP   None            <none>        30000/TCP   20h
#hasura-headless                           ClusterIP   None            <none>        8080/TCP    20h
