---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: dandelion-observability
  namespace: argocd
  # Finalizer that ensures that project is not deleted until it is not referenced by any application
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: Project to host any observability component of the platform
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  destinations:
  - namespace: '*'
    server: '*'
  sourceRepos:
  - '*'
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dandelion-observability
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: dandelion-observability
  source:
    repoURL: {{ .Values.git.repositoryUrl }}
    targetRevision: {{ .Values.git.targetRevision | default "HEAD" }}
    path: applications/observability
    helm:
      parameters:
        # This could be used to test dev branches.
        - name: "git.targetRevision"
          value: {{ .Values.git.targetRevision | default "HEAD" }}
        - name: "dandelion.monitoring.enabled"
          value: {{ .Values.dandelion.monitoring.enabled | quote }}
        - name: "dandelion.logging.enabled"
          value: {{ .Values.dandelion.logging.enabled | quote }}
        - name: "dandelion.provider.name"
          value: {{ .Values.dandelion.provider.name }}
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - ApplyOutOfSyncOnly=true
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true

  destination:
    namespace: argocd
    server: https://kubernetes.default.svc
