apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-node-config
  namespace: cardano-preprod
data:
  # Mithril-based fast sync initialization script
  initContainer-entrypoint: |
    #!/bin/bash
    set -euo pipefail

    echo "Starting Cardano Node initialization with Mithril fast sync..."

    # Check if database already exists and is not empty
    if [[ -d "/data/db" ]] && [[ "$(ls -A /data/db 2>/dev/null)" ]]; then
      echo "Database directory exists and is not empty, skipping restoration"
      exit 0
    fi

    # Create data directory if it doesn't exist
    mkdir -p /data/db

    # Use Mithril client for fast sync on preprod
    if [[ "${RESTORE_SNAPSHOT}" == "true" ]]; then
      echo "Starting Mithril snapshot restoration for preprod network..."
      
      # Download and install mithril-client
      curl -sSL https://github.com/input-output-hk/mithril/releases/latest/download/mithril-client-linux-x64.tar.gz | tar -xz -C /tmp
      chmod +x /tmp/mithril-client
      
      # Set up Mithril for preprod network
      export MITHRIL_AGGREGATOR_ENDPOINT="https://aggregator.testing-preprod.api.mithril.network/aggregator"
      export MITHRIL_GENESIS_VERIFICATION_KEY="5b3132372c37332c3132342c3136312c362c3133372c3133312c3231332c3230372c3131372c3139382c38352c3137362c3139392c3136322c3234312c36382c3132332c3131392c3134372c3133352c3138352c3161312c3138392c3134342c3138392c3135332c3136352c3139392c3135305d"
      
      # Restore from Mithril snapshot
      echo "Downloading Mithril snapshot for preprod..."
      /tmp/mithril-client cardano-db download latest --download-dir /data/db
      
      echo "Mithril snapshot restoration completed"
    fi

  # Preprod network topology configuration
  topology.json: |
    {
      "LocalRoots": {
        "groups": [
          {
            "localRoots": {
              "accessPoints": [],
              "advertise": false
            },
            "valency": 1
          }
        ]
      },
      "PublicRoots": [
        {
          "publicRoots": {
            "accessPoints": [
              {
                "address": "preprod-node.world.dev.cardano.org",
                "port": 30000
              }
            ],
            "advertise": false
          }
        }
      ],
      "useLedgerAfterSlot": 4642000
    }

  # Preprod network node configuration
  config.json: |
    {
      "AlonzoGenesisFile": "/opt/cardano/cnode/files/alonzo-genesis.json",
      "AlonzoGenesisHash": "7e94a15f55d1e82d10f09203fa1d40f8eede58fd8066542cf6566008068ed874",
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 0,
      "ByronGenesisFile": "/opt/cardano/cnode/files/byron-genesis.json",
      "ByronGenesisHash": "d4b8de7a11d929a323373cbab6c1a9bdc931beffff11db111cf9d57356ee1937",
      "ConwayGenesisFile": "/opt/cardano/cnode/files/conway-genesis.json",
      "ConwayGenesisHash": "f28f1c1280ea0d32f8cd3143e268650d6c1a8e221522ce4a7d20d62fc09783e1",
      "EnableP2P": true,
      "LastKnownBlockVersion-Alt": 0,
      "LastKnownBlockVersion-Major": 2,
      "LastKnownBlockVersion-Minor": 0,
      "Protocol": "Cardano",
      "RequiresNetworkMagic": "RequiresMagic",
      "ShelleyGenesisFile": "/opt/cardano/cnode/files/genesis.json",
      "ShelleyGenesisHash": "162d29c4e1cf6b8a84f2d692e67a3ac6bc7851bc3e6e4afe64d15778bed8bd86",
      "TargetNumberOfActivePeers": 20,
      "TargetNumberOfEstablishedPeers": 50,
      "TargetNumberOfKnownPeers": 100,
      "TargetNumberOfRootPeers": 100,
      "TraceAcceptPolicy": true,
      "TraceBlockFetchClient": false,
      "TraceBlockFetchDecisions": false,
      "TraceBlockFetchProtocol": false,
      "TraceBlockFetchProtocolSerialised": false,
      "TraceBlockFetchServer": false,
      "TraceChainDb": true,
      "TraceChainSyncBlockServer": false,
      "TraceChainSyncClient": false,
      "TraceChainSyncHeaderServer": false,
      "TraceChainSyncProtocol": false,
      "TraceConnectionManager": true,
      "TraceDNSResolver": true,
      "TraceDNSSubscription": true,
      "TraceDiffusionInitialization": true,
      "TraceErrorPolicy": true,
      "TraceForge": true,
      "TraceHandshake": false,
      "TraceInboundGovernor": true,
      "TraceIpSubscription": true,
      "TraceLedgerPeers": true,
      "TraceLocalChainSyncProtocol": false,
      "TraceLocalErrorPolicy": true,
      "TraceLocalHandshake": false,
      "TraceLocalRootPeers": true,
      "TraceLocalTxSubmissionProtocol": false,
      "TraceLocalTxSubmissionServer": false,
      "TraceMempool": true,
      "TraceMux": false,
      "TracePeerSelection": true,
      "TracePeerSelectionActions": true,
      "TracePublicRootPeers": true,
      "TraceServer": true,
      "TraceTxInbound": false,
      "TraceTxOutbound": false,
      "TraceTxSubmissionProtocol": false,
      "TracingVerbosity": "NormalVerbosity",
      "TurnOnLogMetrics": true,
      "TurnOnLogging": true,
      "ViewMode": "SimpleView",
      "defaultBackends": ["KatipBK"],
      "defaultScribes": [["StdoutSK", "stdout"]],
      "hasEKG": 12781,
      "hasPrometheus": ["0.0.0.0", 13788],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node-metrics": ["EKGViewBK"],
          "cardano.node.metrics": ["EKGViewBK"]
        }
      },
      "rotation": {
        "rpKeepFilesNum": 10,
        "rpLogLimitBytes": 10000000,
        "rpMaxAgeHours": 24
      },
      "setupBackends": ["KatipBK", "EKGViewBK"],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }

  # Genesis files download script for preprod
  download-genesis.sh: |
    #!/bin/bash
    set -euo pipefail

    echo "Downloading preprod genesis files..."
    mkdir -p /opt/cardano/cnode/files

    # Download preprod genesis files
    curl -o /opt/cardano/cnode/files/byron-genesis.json https://book.world.dev.cardano.org/environments/preprod/byron-genesis.json
    curl -o /opt/cardano/cnode/files/genesis.json https://book.world.dev.cardano.org/environments/preprod/shelley-genesis.json
    curl -o /opt/cardano/cnode/files/alonzo-genesis.json https://book.world.dev.cardano.org/environments/preprod/alonzo-genesis.json
    curl -o /opt/cardano/cnode/files/conway-genesis.json https://book.world.dev.cardano.org/environments/preprod/conway-genesis.json

    echo "Genesis files downloaded successfully"
