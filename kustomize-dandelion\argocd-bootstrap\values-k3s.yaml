dandelion:
  multiNode: false
  provider:
    name: k3s
  monitoring:
    enabled: true
  logging:
    enabled: true
  mainnet:
    enabled: true
    db:
      adminPassword: "CHANGEME"
      roPassword: "CHANGEME"
      repmgrPassword: "CHANGEME"
      diskSize: "150Gi"
    deployIngress: true
    ingressBaseDomain: mainnet.local
    cardano_db_sync:
      enabled: true
    cardano_graphql:
      enabled: true
    cardano_rosetta:
      enabled: true
    metrics_exporter:
      enabled: true
    ogmios:
      enabled: true
    postgrest:
      enabled: true
    cardano_explorer_api:
      enabled: true
    cardano_submit_api:
      enabled: true
    koios:
      enabled: true
  testnet:
    enabled: true
    db:
      adminPassword: "CHANGEME"
      roPassword: "CHANGEME"
      repmgrPassword: "CHANGEME"
      diskSize: "20Gi"
    deployIngress: true
    ingressBaseDomain: testnet.local
    cardano_db_sync:
      enabled: true
    cardano_graphql:
      enabled: true
    cardano_rosetta:
      enabled: true
    metrics_exporter:
      enabled: true
    ogmios:
      enabled: true
    postgrest:
      enabled: true
    cardano_explorer_api:
      enabled: true
    cardano_submit_api:
      enabled: true
    koios:
      enabled: true
