{{- /*
Copyright VMware, Inc.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if (include "postgresql-ha.postgresqlCreateSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "postgresql-ha.postgresql" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: postgresql
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if and (include "postgresql-ha.postgresqlPostgresPassword" .) (not (eq (include "postgresql-ha.postgresqlUsername" .) "postgres")) }}
  postgres-password: {{ include "postgresql-ha.postgresqlPostgresPassword" . | b64enc | quote }}
  {{- end }}
  password: {{ (include "postgresql-ha.postgresqlPassword" .) | b64enc | quote }}
  repmgr-password: {{ (include "postgresql-ha.postgresqlRepmgrPassword" .) | b64enc | quote }}
{{- end -}}
