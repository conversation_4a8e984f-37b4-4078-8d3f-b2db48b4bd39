apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-node
  labels:
    io.kompose.service: cardano-node
spec:
  serviceName: cardano-node
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: cardano-node
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        io.kompose.service: cardano-node
    spec:
      initContainers:
      # Clean up any lost+found directories
      - name: remove-lost-n-found
        image: busybox
        imagePullPolicy: IfNotPresent
        command: ["sh", "-c", "rm -rf /data/db/lost+found"]
        volumeMounts:
        - mountPath: /data/db
          name: node-db
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      
      # Download genesis files
      - name: download-genesis
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        imagePullPolicy: IfNotPresent
        command: ["bash", "/configmap/download-genesis.sh"]
        volumeMounts:
        - name: cardano-node-config
          mountPath: /configmap
        - name: genesis-files
          mountPath: /opt/cardano/cnode/files
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      
      # Mithril fast sync restoration
      - name: mithril-restore
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        imagePullPolicy: IfNotPresent
        command: ["bash", "-x", "/configmap/initContainer-entrypoint"]
        env:
        - name: RESTORE_SNAPSHOT
          value: "true"
        - name: NETWORK
          value: preprod
        - name: CNODE_DB_PATH
          value: /data
        volumeMounts:
        - name: cardano-node-config
          mountPath: /configmap
        - mountPath: /data/db
          name: node-db
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      
      containers:
      # Main cardano-node container
      - name: cardano-node
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        imagePullPolicy: IfNotPresent
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: CARDANO_NODE_SOCKET_PATH
          value: /ipc/node.socket
        command:
        - cardano-node
        - run
        - --config
        - /configmap/config.json
        - --topology
        - /configmap/topology.json
        - --database-path
        - /data/db
        - --socket-path
        - /ipc/node.socket
        - --host-addr
        - 0.0.0.0
        - --port
        - "3001"
        ports:
        - containerPort: 3001
          name: cardano-node
        - containerPort: 12781
          name: ekg
        - containerPort: 13788
          name: prometheus
        volumeMounts:
        - mountPath: /data/db
          name: node-db
        - mountPath: /ipc
          name: node-ipc
        - mountPath: /configmap
          name: cardano-node-config
        - mountPath: /opt/cardano/cnode/files
          name: genesis-files
        resources:
          requests:
            memory: "4Gi"
            cpu: "2000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        # Health check equivalent to docker-compose EKG check
        livenessProbe:
          httpGet:
            path: /
            port: 12781
          initialDelaySeconds: 300
          periodSeconds: 60
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          exec:
            command:
            - test
            - -S
            - /ipc/node.socket
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
      
      # Socket relay container for db-sync communication
      - name: socat-tcp-server
        image: alpine/socat
        env:
        - name: PORT
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_TCP_PORT
        command:
        - sh
        - -c
        - socat TCP-LISTEN:${PORT},fork UNIX-CLIENT:/ipc/node.socket,ignoreeof
        ports:
        - containerPort: 30000
          name: socket-relay
        volumeMounts:
        - mountPath: /ipc
          name: node-ipc
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
      
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: genesis-files
        emptyDir: {}
      - name: cardano-node-config
        configMap:
          name: cardano-node
          defaultMode: 0755
      
      restartPolicy: Always
  
  volumeClaimTemplates:
  - metadata:
      name: node-db
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
