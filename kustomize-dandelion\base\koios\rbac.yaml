---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: koios-deploy-sql
automountServiceAccountToken: true

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: koios-deploy-sql
rules:
- apiGroups: [""] # "" indicates the core API group
  resources: ["pods"]
  verbs: ["delete", "get", "watch", "list", "patch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: koios-deploy-sql
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: koios-deploy-sql
subjects:
- kind: ServiceAccount
  name: koios-deploy-sql
