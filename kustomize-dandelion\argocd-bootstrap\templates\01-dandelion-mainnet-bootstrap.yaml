{{ if .Values.dandelion.mainnet.enabled }}
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: dandelion-mainnet{{ .Values.dandelion.argoAppSuffix }}
  namespace: argocd
  # Finalizer that ensures that project is not deleted until it is not referenced by any application
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: dandelion-mainnet{{ .Values.dandelion.argoAppSuffix }}
  clusterResourceWhitelist:
  - group: '*'
    kind: '*'
  destinations:
  - namespace: '*'
    server: '*'
  sourceRepos:
  - '*'
---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dandelion-mainnet{{ .Values.dandelion.argoAppSuffix }}
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: dandelion-mainnet{{ .Values.dandelion.argoAppSuffix }}
  source:
    repoURL: {{ .Values.git.repositoryUrl }}
    targetRevision: {{ .Values.git.targetRevision | default "HEAD" }}
    path: applications/main-app
    helm:
      parameters:
        # This could be used to test dev branches.
        - name: "git.targetRevision"
          value: {{ .Values.git.targetRevision | default "HEAD" }}
        - name: "dandelion.cardano.network"
          value: "mainnet"
        - name: "dandelion.provider.name"
          value: {{ .Values.dandelion.provider.name }}
        - name: "dandelion.argoProjectSuffix"
          value: {{ .Values.dandelion.argoProjectSuffix }}
        - name: "dandelion.argoAppSuffix"
          value: {{ .Values.dandelion.argoAppSuffix }}
        - name: "dandelion.namespaceSuffix"
          value: {{ .Values.dandelion.namespaceSuffix }}
      values: |
        dandelion:
        {{ with .Values.dandelion.mainnet }}
{{ toYaml . | indent 10 }}
        {{ end }}
      valueFiles:
        - values.yaml
        - values-{{ .Values.dandelion.provider.name }}.yaml
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - ApplyOutOfSyncOnly=true
  destination:
    namespace: argocd
    server: https://kubernetes.default.svc
{{ end }}
