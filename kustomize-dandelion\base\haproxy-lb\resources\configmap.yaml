apiVersion: v1
kind: ConfigMap
metadata:
  name: dandelion-haproxy-configmap
data:
  topology-disabled: |
          { "name": "TICKR",        "addr": "localhost", "port": 443, "protocol": "https" },
  topology-iohk-preprod.json: |  
    {
      "Providers": [
          { "name": "PEACE-es",       "addr": "rathole-service.rathole.svc.cluster.local", "port": 4430, "protocol": "https" },
          { "name": "PEACE-fr",       "addr": "em0.fr.scw.dandelion.link", "port": 443, "protocol": "https" },
          { "name": "GAMECHANGER",    "addr": "rathole-service.rathole.svc.cluster.local", "port": 4431, "protocol": "https" }
      ],
      "Consumers": []
    }
  topology-iohk-preview.json: |
    {
      "Providers": [
          { "name": "GAMECHANGER",    "addr": "rathole-service.rathole.svc.cluster.local", "port": 4431, "protocol": "https" }
      ],
      "Consumers": []
    }
  topology-mainnet.json: |
    {
      "Providers": [
          { "name": "PEACE-fr",       "addr": "em0.fr.scw.dandelion.link", "port": 443, "protocol": "https" },
          { "name": "GAMECHANGER",    "addr": "rathole-service.rathole.svc.cluster.local", "port": 4431, "protocol": "https" }
      ],
      "Consumers": []
    }
  disabled-graphql-api-healthcheck.tpl: |
    option httpchk
    http-check send meth GET uri / ver HTTP/1.1 hdr host ${INGRESS_DOMAIN}
    http-check expect rstring GET.query.missing
  graphql-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/graphql-api-hc.sh
  submit-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/submit-api-hc.sh
  explorer-api-healthcheck.tpl: |
    option httpchk
    http-check send meth GET uri /api/txs/last ver HTTP/1.1 hdr host ${INGRESS_DOMAIN}
    http-check expect string cteId
  ogmios-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/ogmios-hc.sh
  kupo-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/kupo-api-hc.sh
  postgrest-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/postgrest-api-hc.sh
  koios-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/koios-api-hc.sh
  rosetta-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/rosetta-api-hc.sh
  blockfrost-api-healthcheck.tpl: |
    option external-check
    external-check path "/usr/bin:/bin:/tmp:/sbin:/usr/sbin"
    external-check command /configmap/blockfrost-api-hc.sh
  cardano-token-registry-api-healthcheck.tpl: |
    option httpchk
    http-check send meth GET uri / ver HTTP/1.1 hdr host ${INGRESS_DOMAIN}
    http-check expect rstring JSON.Server
  drift-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed "s|^d.||g")
    HEALTHCHECK_TIMEOUT=5
    MAX_DRIFT=${CARDANO_MAX_TIP_DRIFT:-600}

    dbtip=$(timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -fL -H "Host: ${HOST_HEADER}" -H "Accept: text/plain" "https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/block?select=time::text&order=id.desc&limit=1" 2>/dev/null)
    currtip=$(TZ='UTC' date "+%Y-%m-%d %H:%M:%S")
    if [[ -n "${dbtip}" ]] ; then
      TIP_DRIFT=$(( $(date -d "${currtip}" +%s) - $(date -d "${dbtip}" +%s) ))
      if [[ ${TIP_DRIFT} -lt ${MAX_DRIFT} ]]
      then
        echo "[OK][${HC_PID}] (${HOST_HEADER}): Tip ok (last block timestamp: ${dbtip}, tip-drift: -${TIP_DRIFT}) (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT})"
        exit 0
      else
        echo "[ERROR][${HC_PID}] (${HOST_HEADER}): Tip too far (last block timestamp: ${dbtip}, tip-drift: -${TIP_DRIFT}), eliminating from availability (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})"
        exit 1
      fi
    else
      echo "[ERROR][${HC_PID}] (${HOST_HEADER}): Failed polling for ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, could not query tip"
      exit 2
    fi
  graphql-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|graphql|postgrest|g') bash /configmap/drift-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi
    HEALTHCHECK_TIMEOUT=5

    timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -H "Host: ${HOST_HEADER}" -H 'Accept: application/json' -H 'Content-Type: application/json' --data-binary '{"query":"# Write your query or mutation here\nquery chainTipAndCurrentEpochNumber {\n  cardano {\n    tip {\n      number\n    }\n    currentEpoch {\n      number\n    }\n  }\n}"}' https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT} | jq -r .data.cardano.tip.number 2>/dev/null | grep -q ^[0-9] || ( echo "[ERROR][${HC_PID}] (${HOST_HEADER}): no tip received (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})" && exit 2 )
  koios-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|koios|postgrest|g') bash /configmap/drift-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi
    HEALTHCHECK_TIMEOUT=5

    timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -H "Host: ${HOST_HEADER}" "https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/rpc/tip" | grep -q hash || ( echo "[ERROR][${HC_PID}] (${HOST_HEADER}): no tip received (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})" && exit 2 )
  postgrest-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|postgrest|postgrest|g') bash /configmap/drift-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi
    HEALTHCHECK_TIMEOUT=5

    timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -H "Host: ${HOST_HEADER}" "https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/epoch?limit=1" | grep -q out_sum || ( echo "[ERROR][${HC_PID}] (${HOST_HEADER}: no tip received (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})" && exit 2 )

  rosetta-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|rosetta|postgrest|g') bash /configmap/drift-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi
    HEALTHCHECK_TIMEOUT=5

    timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -H "Host: ${HOST_HEADER}" "https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/" | grep -q message.*Route.GET.*404 || ( echo "[ERROR][${HC_PID}] (${HOST_HEADER}): bad response (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})" && exit 2 )

  blockfrost-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|blockfrost|postgrest|g') bash /configmap/drift-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi
    HEALTHCHECK_TIMEOUT=5

    timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -H "Host: ${HOST_HEADER}" "https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/health" | grep -q is_healthy.*true || ( echo "[ERROR][${HC_PID}] (${HOST_HEADER}): bad response (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})" && exit 2 )

  submit-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    HEALTHCHECK_TIMEOUT=5
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|submit|ogmios|g') bash /configmap/ogmios-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi

    HTTP_STATUS=$(timeout ${HEALTHCHECK_TIMEOUT} curl -w %{http_code} -ks -o /dev/null -H "Host: ${HOST_HEADER}" -X POST https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/api/submit/tx)
    if [ ${HTTP_STATUS} -ne 415 ]
    then
      exit 2
    fi

  ogmios-hc.sh: |
    #!/usr/bin/env bash
    
    HC_PID=$$
    HEALTHCHECK_TIMEOUT=5
    MAX_DRIFT=${CARDANO_MAX_TIP_DRIFT:-600}
    MIN_NETWORK_SYNC="0.99995"

    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')

    networkSynchronization=$(timeout ${HEALTHCHECK_TIMEOUT} curl -ks -H "Host: ${HOST_HEADER}" -H 'Accept: application/json' https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/health | jq -r .networkSynchronization 2>/dev/null)

    if [ -z ${networkSynchronization} ]
    then
      echo "[ERROR][${HC_PID}] ($HOST_HEADER): Could not get 'networkSynchronization' (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT})"
      exit 1
    fi

    if [ 1 -eq "$(echo "${networkSynchronization} < ${MIN_NETWORK_SYNC}" | bc 2>/dev/null)" ]
    then
      echo "[ERROR][${HC_PID}] ($HOST_HEADER): Network Synchronization is ${networkSynchronization}, less than the required ${MIN_NETWORK_SYNC} (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT})"
      exit 1
    fi

    ogmiosLastTipUpdate=$(timeout ${HEALTHCHECK_TIMEOUT} curl -ks -H "Host: ${HOST_HEADER}" -H 'Accept: application/json' https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/health | jq -r .lastTipUpdate)

    currtip=$(TZ='UTC' date "+%Y-%m-%d %H:%M:%S")
    if [[ -n "${ogmiosLastTipUpdate}" ]] ; then
      TIP_DRIFT=$(( $(date -d "${currtip}" +%s) - $(date -d "${ogmiosLastTipUpdate}" +%s) ))
      if [[ ${TIP_DRIFT} -lt ${MAX_DRIFT} ]]
      then
        echo "[OK][${HC_PID}] (${HOST_HEADER}}: Tip ok (last block timestamp: ${ogmiosLastTipUpdate}, tip-drift: -${TIP_DRIFT}) (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT})"
        exit 0
      else
        echo "[ERROR][${HC_PID}] (${HOST_HEADER}): Tip too far (last block timestamp: ${ogmiosLastTipUpdate}, tip-drift: -${TIP_DRIFT}), eliminating from availability (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})"
        exit 1
      fi
    else
      echo "[ERROR][${HC_PID}] (${HOST_HEADER}): Failed polling for ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, could not query tip"
      exit 2
    fi

  kupo-api-hc.sh: |
    #!/usr/bin/env bash

    HC_PID=$$
    export HOST_HEADER=$(echo $HAPROXY_PROXY_NAME | sed 's|^d.||g')
    HAPROXY_PROXY_NAME=$(echo $HAPROXY_PROXY_NAME | sed 's|kupo|postgrest|g') bash /configmap/drift-hc.sh
    if [ $? -ne 0 ]; then exit 1; fi
    HEALTHCHECK_TIMEOUT=5

    timeout ${HEALTHCHECK_TIMEOUT} curl -s -k -H "Host: ${HOST_HEADER}" "https://${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}/health" | grep -q kupo_most_recent_node_tip || ( echo "[ERROR][${HC_PID}] (${HOST_HEADER}: no tip received (server: ${HAPROXY_SERVER_ADDR}:${HAPROXY_SERVER_PORT}, timeout: ${HEALTHCHECK_TIMEOUT})" && exit 2 )

  api-config.json: |
    {
      "graphql-api": { "checkInterval": "16s" },
      "postgrest-api": { "checkInterval": "8s", "retryOn": "404" },
      "koios-api": { "checkInterval": "8s" },
      "rosetta-api": { "checkInterval": "20s" },
      "submit-api": { "checkInterval": "20s" },
      "explorer-api": { "checkInterval": "20s" },
      "ogmios-api": { "checkInterval": "20s" }
    }
  dis: |
      "postgrest-api": { "checkInterval": "8s", "retryOn": "conn-failure empty-response junk-response response-timeout 0rtt-rejected 500 502 503 504 404" },
  initContainer-entrypoint: |
    #!/bin/bash

    rm -f ${CONFIG_OUTPUT_DIR}/dandelion-haproxy.cfg
    
    # FRONTEND conf
    cat > ${CONFIG_OUTPUT_DIR}/dandelion-haproxy.cfg <<EOF
      frontend metrics
        mode http
        bind :1024
        stats enable
        stats uri /
        stats hide-version
        stats refresh 10s
        stats admin if LOCALHOST
        option http-use-htx
        http-request use-service prometheus-exporter if { path /metrics }

      frontend main
        mode http
        bind :80
        bind :443 ssl crt /usr/local/etc/ssl/self-signed-ssl.pem
        http-request redirect scheme https code 301 unless { ssl_fc }

        acl is_upgrade hdr(Connection) -i upgrade
        acl is_websocket hdr(Upgrade) -i websocket
    
        acl valid_hosts hdr(host) -m reg -i (${SUB_DOMAIN}.|)($(echo $APIS | sed 's/ /|/g')).($(echo $NETWORKS | sed 's/ /|/g')).${TOP_DOMAIN}$
        acl valid_hosts hdr(host) -m reg -i stats-ian.dandelion.link

        # deny examples:
        acl denied path_beg,url_dec -i /graphql
        acl denied req.body -m reg .*BlockOverView.*
        acl denied req.body -m reg .*activeStake_aggregate.*epochNo.*326.*
        acl denied hdr(X-Forwarded-For) -m reg -i **************

        http-request deny if !valid_hosts
        http-request deny if denied

        acl cf_ip_hdr  req.hdr(CF-Connecting-IP) -m found
        http-request set-header X-Forwarded-For %[req.hdr(CF-Connecting-IP)] if cf_ip_hdr

        option http-buffer-request

        declare capture request len 256
        http-request capture req.hdr(X-Forwarded-For) id 0

        declare capture request len 5000000
        http-request capture req.body id 1

        log-format "%ci:%cp (X-Forwarded-For: %[capture.req.hdr(0)]) [%tr] %ft %b/%s %TR/%Tw/%Tc/%Tr/%Ta %ST %B %CC %CS %tsc %ac/%fc/%bc/%sc/%rc %sq/%bq %hs %{+Q}r %[capture.req.hdr(1)]"
    
        use_backend %[req.hdr(Host),lower]
        default_backend no-match
    EOF
    
    # BACKENDS conf

    # stats
    cat >> ${CONFIG_OUTPUT_DIR}/dandelion-haproxy.cfg <<EOF
      backend stats-ian.dandelion.link
        mode http
        stats enable
        stats uri /
        stats hide-version
        stats refresh 10s
        stats admin if LOCALHOST

        option http-use-htx
        http-request use-service prometheus-exporter if { path /metrics }
    EOF

    for network in ${NETWORKS}
    do
    
      for api in ${APIS}
      do
    
        export FQDN="${SUB_DOMAIN}.${api}.${network}.${TOP_DOMAIN}"
        export INGRESS_DOMAIN="${api}.${network}.${TOP_DOMAIN}"
        API_CHECK_INTERVAL=$(jq -r .\"${api}\".checkInterval /configmap/api-config.json) 
        if [ "${API_CHECK_INTERVAL}" != "null" ]; then export HAPROXY_CHECK_INTERVAL="inter ${API_CHECK_INTERVAL}"; else unset HAPROXY_CHECK_INTERVAL; fi
        RETRY_ON=$(jq -r .\"${api}\".retryOn /configmap/api-config.json) 
        if [ "${RETRY_ON}" != "null" ]; then export HAPROXY_BACKEND_RETRY_ON="${RETRY_ON}"; else unset HAPROXY_BACKEND_RETRY_ON; fi
    
        jq -rc .Providers[] ${TOPOLOGY_DIR}/topology-${network}.json | while read provider
        do
          PROVIDER_NAME=$(echo $provider | jq -r .name)
          PROVIDER_HOST=$(echo $provider | jq -r .addr)
          PROVIDER_PORT=$(echo $provider | jq -r .port)
          PROVIDER_PROTO=$(echo $provider | jq -r .protocol)
          PROVIDER_SNI=$(echo $provider | jq -r .sni)

          if [ "${PROVIDER_PROTO}" == "https" ]
          then
            if [ "${PROVIDER_SNI}" != "null" ]
            then
              SNI_FQDN=${PROVIDER_SNI}
            else
              SNI_FQDN=${INGRESS_DOMAIN}
            fi
            CHECK="check ${HAPROXY_CHECK_INTERVAL:-inter 20s} ssl verify none check-sni ${INGRESS_DOMAIN} sni str(${SNI_FQDN})"
          else
            CHECK="check ${HAPROXY_CHECK_INTERVAL}"
          fi
          echo "    server ${PROVIDER_NAME}    ${PROVIDER_HOST}:${PROVIDER_PORT} ${CHECK} maxconn ${HAPROXY_MAXCONN_PER_BACKEND} cookie core init-addr none resolvers resolvconf resolve-opts allow-dup-ip"
        done > /tmp/${FQDN}.backends.haproxy.cfg 
      
        cat > /tmp/${FQDN}.haproxy.cfg <<EOF
      backend ${FQDN} 
        mode http
        balance static-rr
        retry-on ${HAPROXY_BACKEND_RETRY_ON:-all-retryable-errors}

        $(envsubst < ${HEALTHCHECKS_TEMPLATE_DIR}/${api}-healthcheck.tpl)
    
        cookie network insert
        http-request set-header X-Client-IP %[src]
        http-request set-header X-Forwarded-Host %[req.hdr(Host)]
        http-request set-header Origin https://${INGRESS_DOMAIN}
        http-request set-header Host ${INGRESS_DOMAIN}
        http-response set-header Access-Control-Allow-Origin *
    
    $(cat /tmp/${FQDN}.backends.haproxy.cfg)
    
    EOF

      cat /tmp/${FQDN}.haproxy.cfg >> ${CONFIG_OUTPUT_DIR}/dandelion-haproxy.cfg
      done
    done
